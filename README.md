# FilmStream - OTT Platform

A comprehensive SaaS PWA video OTT MVC platform built in pure PHP and MySQL, designed by filmmakers for filmmakers. This platform enables filmmakers to create their own streaming platforms similar to IndieFilx/Uscreen with subscription-based content, pay-per-view options, and a centralized catalog.

## 🎬 Features

### Core Platform Features
- **Filmmaker-Centric Design**: Built specifically for independent filmmakers
- **Multi-Tenant Architecture**: Each filmmaker gets their own customizable platform
- **Centralized Content Catalog**: Discover content across all filmmaker platforms
- **Flexible Monetization**: Subscription-based and pay-per-view options
- **Progressive Web App (PWA)**: Mobile-first, app-like experience

### Technical Features
- **Pure PHP MVC Architecture**: Clean, maintainable codebase
- **RESTful API**: Complete API for all platform functionality
- **Alpine.js Frontend**: Reactive, lightweight JavaScript framework
- **Tailwind CSS**: Utility-first CSS with dark/light mode support
- **Advanced Routing**: Flexible routing with middleware support
- **JWT Authentication**: Secure token-based authentication
- **2FA Support**: App-based two-factor authentication

### Integrations
- **Stripe Connect Express**: Automated filmmaker payouts with platform commissions
- **Bunny.net Stream API**: Video encoding, storage, and delivery
- **Secure Video URLs**: Encrypted video URLs to prevent direct access

## 🚀 Quick Start

### Prerequisites
- PHP 8.1 or higher
- MySQL 5.7 or higher
- Composer
- Web server (Apache/Nginx)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-repo/filmstream.git
   cd filmstream
   ```

2. **Install dependencies**
   ```bash
   composer install
   ```

3. **Environment setup**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Generate application key**
   ```bash
   php -r "echo 'APP_KEY=' . bin2hex(random_bytes(16)) . PHP_EOL;"
   ```

5. **Set up database**
   ```bash
   # Create database and run migrations (coming soon)
   php artisan migrate
   ```

6. **Start development server**
   ```bash
   composer serve
   # or
   php -S localhost:8000 -t public
   ```

## 📁 Project Structure

```
filmstream/
├── app/                    # Application code
│   ├── Controllers/        # HTTP controllers
│   ├── Models/            # Data models
│   ├── Middleware/        # HTTP middleware
│   ├── Services/          # Business logic services
│   ├── Core/              # Core framework classes
│   └── Helpers/           # Helper functions
├── config/                # Configuration files
├── database/              # Database migrations and seeds
├── public/                # Web server document root
│   ├── assets/           # Static assets (CSS, JS, images)
│   └── index.php         # Application entry point
├── resources/             # Views and language files
│   ├── views/            # HTML templates
│   └── lang/             # Localization files
├── routes/                # Route definitions
│   ├── web.php           # Web routes
│   └── api.php           # API routes
├── storage/               # Application storage
│   ├── logs/             # Log files
│   ├── cache/            # Cache files
│   └── sessions/         # Session files
├── tests/                 # Test files
└── vendor/                # Composer dependencies
```

## 🔧 Configuration

### Environment Variables

Key environment variables to configure:

```env
# Application
APP_NAME="FilmStream"
APP_ENV=local
APP_DEBUG=true
APP_URL=http://localhost:8000

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_DATABASE=filmstream_ott
DB_USERNAME=root
DB_PASSWORD=

# Stripe
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_CONNECT_CLIENT_ID=ca_...

# Bunny.net Stream
BUNNY_STREAM_API_KEY=...
BUNNY_STREAM_LIBRARY_ID=...
BUNNY_STREAM_HOSTNAME=...

# Security
JWT_SECRET=your-secret-key
ENCRYPTION_KEY=your-encryption-key
```

## 🏗️ Architecture

### MVC Pattern
- **Models**: Data layer with database interactions
- **Views**: Presentation layer with HTML templates
- **Controllers**: Business logic and request handling

### Middleware Pipeline
- CORS handling
- Security headers
- Rate limiting
- Authentication
- Authorization

### Dependency Injection
- Service container for dependency management
- Automatic dependency resolution
- Singleton and transient services

## 🔐 Security Features

- **JWT Authentication**: Secure token-based auth
- **2FA Support**: TOTP-based two-factor authentication
- **Password Hashing**: Bcrypt with configurable rounds
- **CSRF Protection**: Cross-site request forgery protection
- **Rate Limiting**: IP-based request throttling
- **Secure Headers**: Comprehensive security headers
- **Video URL Encryption**: Prevent direct video access

## 📊 Development Status

This is an active development project. Current status:

- [x] Core MVC framework
- [x] Routing system
- [x] Middleware pipeline
- [x] Basic authentication structure
- [ ] Database schema and models
- [ ] User management system
- [ ] Filmmaker platform creation
- [ ] Content management
- [ ] Payment integration
- [ ] Video streaming
- [ ] Frontend views

## 🤝 Contributing

This project is designed to be filmmaker-friendly and community-driven. Contributions are welcome!

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🎯 Roadmap

### Phase 1: Foundation (Current)
- Core framework completion
- Database schema design
- Basic authentication

### Phase 2: Core Features
- User management
- Filmmaker onboarding
- Content management system

### Phase 3: Monetization
- Stripe Connect integration
- Subscription management
- Pay-per-view system

### Phase 4: Streaming
- Bunny.net integration
- Video player
- Analytics

### Phase 5: Enhancement
- Advanced features
- Mobile apps
- Performance optimization

---

**Built with ❤️ by filmmakers, for filmmakers**
