<?php

declare(strict_types=1);

namespace App\Controllers;

use App\Core\Controller;
use App\Core\Container;
use App\Core\Http\Request;
use App\Core\Http\JsonResponse;
use App\Models\User;
use Exception;

/**
 * Admin Controller
 * Handles administrative functions and user management
 */
class AdminController extends Controller
{
    public function __construct(Container $container)
    {
        parent::__construct($container);
    }

    /**
     * Get dashboard statistics
     */
    public function getDashboardStats(Request $request): JsonResponse
    {
        try {
            $this->requireAuth();
            $this->requireRole('admin');

            // Get user statistics
            $userStats = $this->db->fetchOne("
                SELECT 
                    COUNT(*) as total_users,
                    SUM(CASE WHEN role = 'viewer' THEN 1 ELSE 0 END) as viewers,
                    SUM(CASE WHEN role = 'filmmaker' THEN 1 ELSE 0 END) as filmmakers,
                    SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_users,
                    SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as new_users_30d
                FROM users 
                WHERE deleted_at IS NULL
            ");

            // Get platform statistics
            $platformStats = $this->db->fetchOne("
                SELECT 
                    COUNT(*) as total_platforms,
                    SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_platforms,
                    SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as new_platforms_30d
                FROM filmmaker_platforms
            ");

            // Get content statistics
            $contentStats = $this->db->fetchOne("
                SELECT 
                    COUNT(*) as total_content,
                    SUM(CASE WHEN status = 'published' THEN 1 ELSE 0 END) as published_content,
                    SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as new_content_30d
                FROM content
            ");

            // Get verification queue
            $verificationQueue = $this->db->fetchOne("
                SELECT COUNT(*) as pending_verifications
                FROM filmmaker_profiles 
                WHERE verification_status = 'pending'
            ");

            return $this->success([
                'users' => $userStats,
                'platforms' => $platformStats ?: ['total_platforms' => 0, 'active_platforms' => 0, 'new_platforms_30d' => 0],
                'content' => $contentStats ?: ['total_content' => 0, 'published_content' => 0, 'new_content_30d' => 0],
                'verification_queue' => $verificationQueue['pending_verifications'] ?? 0
            ], 'Dashboard statistics retrieved successfully');

        } catch (Exception $e) {
            $this->logger->error('Get dashboard stats failed: ' . $e->getMessage());
            return $this->error('Failed to retrieve dashboard statistics');
        }
    }

    /**
     * List all users with filtering
     */
    public function listUsers(Request $request): JsonResponse
    {
        try {
            $this->requireAuth();
            $this->requireRole('admin');

            $pagination = $this->getPaginationParams();
            $search = $request->query('search', '');
            $role = $request->query('role', '');
            $status = $request->query('status', '');

            $sql = "SELECT id, uuid, email, first_name, last_name, username, role, status, 
                           email_verified_at, last_login_at, created_at
                    FROM users 
                    WHERE deleted_at IS NULL";

            $params = [];

            if ($search) {
                $sql .= " AND (first_name LIKE :search OR last_name LIKE :search 
                         OR email LIKE :search OR username LIKE :search)";
                $params['search'] = "%{$search}%";
            }

            if ($role) {
                $sql .= " AND role = :role";
                $params['role'] = $role;
            }

            if ($status) {
                $sql .= " AND status = :status";
                $params['status'] = $status;
            }

            $sql .= " ORDER BY created_at DESC LIMIT :limit OFFSET :offset";
            $params['limit'] = $pagination['perPage'];
            $params['offset'] = $pagination['offset'];

            $users = $this->db->fetchAll($sql, $params);

            // Get total count
            $countSql = str_replace(
                "SELECT id, uuid, email, first_name, last_name, username, role, status, 
                           email_verified_at, last_login_at, created_at",
                "SELECT COUNT(*)",
                $sql
            );
            $countSql = preg_replace('/ORDER BY.*LIMIT.*OFFSET.*/', '', $countSql);
            
            unset($params['limit'], $params['offset']);
            $total = $this->db->query($countSql, $params)->fetchColumn();

            return $this->paginated($users, (int)$total, $pagination['page'], $pagination['perPage']);

        } catch (Exception $e) {
            $this->logger->error('List users failed: ' . $e->getMessage());
            return $this->error('Failed to retrieve users');
        }
    }

    /**
     * Update user status
     */
    public function updateUserStatus(Request $request, string $id): JsonResponse
    {
        try {
            $this->requireAuth();
            $this->requireRole('admin');

            $data = $this->validate([
                'status' => 'required|in:active,inactive,suspended,pending_verification'
            ]);

            $user = User::find($this->db, (int)$id);
            if (!$user) {
                return $this->error('User not found', 404);
            }

            $oldStatus = $user->status;
            $user->status = $data['status'];
            $user->save();

            // Log activity
            $this->logActivity('user_status_updated', 'User status updated by admin', [
                'target_user_id' => $user->id,
                'old_status' => $oldStatus,
                'new_status' => $data['status']
            ]);

            return $this->success([
                'user' => $user->toArray()
            ], 'User status updated successfully');

        } catch (Exception $e) {
            $this->logger->error('Update user status failed: ' . $e->getMessage());
            return $this->error('Failed to update user status');
        }
    }

    /**
     * Get verification queue
     */
    public function getVerificationQueue(Request $request): JsonResponse
    {
        try {
            $this->requireAuth();
            $this->requireRole('admin');

            $pagination = $this->getPaginationParams();

            $sql = "SELECT fp.*, u.first_name, u.last_name, u.email, u.username
                    FROM filmmaker_profiles fp
                    JOIN users u ON fp.user_id = u.id
                    WHERE fp.verification_status = 'pending'
                    ORDER BY fp.created_at ASC
                    LIMIT :limit OFFSET :offset";

            $queue = $this->db->fetchAll($sql, [
                'limit' => $pagination['perPage'],
                'offset' => $pagination['offset']
            ]);

            // Get total count
            $total = $this->db->query(
                "SELECT COUNT(*) FROM filmmaker_profiles WHERE verification_status = 'pending'"
            )->fetchColumn();

            return $this->paginated($queue, (int)$total, $pagination['page'], $pagination['perPage']);

        } catch (Exception $e) {
            $this->logger->error('Get verification queue failed: ' . $e->getMessage());
            return $this->error('Failed to retrieve verification queue');
        }
    }

    /**
     * Approve filmmaker verification
     */
    public function approveVerification(Request $request, string $userId): JsonResponse
    {
        try {
            $this->requireAuth();
            $this->requireRole('admin');

            $profile = $this->db->fetchOne(
                "SELECT * FROM filmmaker_profiles WHERE user_id = :user_id",
                ['user_id' => (int)$userId]
            );

            if (!$profile) {
                return $this->error('Filmmaker profile not found', 404);
            }

            if ($profile['verification_status'] !== 'pending') {
                return $this->error('Profile is not pending verification');
            }

            // Update verification status
            $this->db->update('filmmaker_profiles', [
                'verification_status' => 'verified',
                'verified_at' => date('Y-m-d H:i:s')
            ], ['user_id' => (int)$userId]);

            // Log activity
            $this->logActivity('verification_approved', 'Filmmaker verification approved', [
                'target_user_id' => (int)$userId
            ]);

            return $this->success([], 'Filmmaker verification approved successfully');

        } catch (Exception $e) {
            $this->logger->error('Approve verification failed: ' . $e->getMessage());
            return $this->error('Failed to approve verification');
        }
    }

    /**
     * Reject filmmaker verification
     */
    public function rejectVerification(Request $request, string $userId): JsonResponse
    {
        try {
            $this->requireAuth();
            $this->requireRole('admin');

            $data = $this->validate([
                'reason' => 'required|max:1000'
            ]);

            $profile = $this->db->fetchOne(
                "SELECT * FROM filmmaker_profiles WHERE user_id = :user_id",
                ['user_id' => (int)$userId]
            );

            if (!$profile) {
                return $this->error('Filmmaker profile not found', 404);
            }

            if ($profile['verification_status'] !== 'pending') {
                return $this->error('Profile is not pending verification');
            }

            // Update verification status
            $this->db->update('filmmaker_profiles', [
                'verification_status' => 'rejected'
            ], ['user_id' => (int)$userId]);

            // Log activity
            $this->logActivity('verification_rejected', 'Filmmaker verification rejected', [
                'target_user_id' => (int)$userId,
                'reason' => $data['reason']
            ]);

            return $this->success([], 'Filmmaker verification rejected');

        } catch (Exception $e) {
            $this->logger->error('Reject verification failed: ' . $e->getMessage());
            return $this->error('Failed to reject verification');
        }
    }

    /**
     * Get system activity logs
     */
    public function getActivityLogs(Request $request): JsonResponse
    {
        try {
            $this->requireAuth();
            $this->requireRole('admin');

            $pagination = $this->getPaginationParams();
            $action = $request->query('action', '');
            $userId = $request->query('user_id', '');

            $sql = "SELECT al.*, u.first_name, u.last_name, u.email
                    FROM activity_logs al
                    LEFT JOIN users u ON al.user_id = u.id
                    WHERE 1=1";

            $params = [];

            if ($action) {
                $sql .= " AND al.action = :action";
                $params['action'] = $action;
            }

            if ($userId) {
                $sql .= " AND al.user_id = :user_id";
                $params['user_id'] = (int)$userId;
            }

            $sql .= " ORDER BY al.created_at DESC LIMIT :limit OFFSET :offset";
            $params['limit'] = $pagination['perPage'];
            $params['offset'] = $pagination['offset'];

            $logs = $this->db->fetchAll($sql, $params);

            // Get total count
            $countSql = str_replace(
                "SELECT al.*, u.first_name, u.last_name, u.email",
                "SELECT COUNT(*)",
                $sql
            );
            $countSql = preg_replace('/ORDER BY.*LIMIT.*OFFSET.*/', '', $countSql);
            
            unset($params['limit'], $params['offset']);
            $total = $this->db->query($countSql, $params)->fetchColumn();

            return $this->paginated($logs, (int)$total, $pagination['page'], $pagination['perPage']);

        } catch (Exception $e) {
            $this->logger->error('Get activity logs failed: ' . $e->getMessage());
            return $this->error('Failed to retrieve activity logs');
        }
    }
}
