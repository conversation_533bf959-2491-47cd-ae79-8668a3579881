<?php

declare(strict_types=1);

namespace App\Controllers;

use App\Core\Controller;
use App\Core\Container;
use App\Core\Http\Request;
use App\Core\Http\JsonResponse;
use App\Models\User;
use App\Services\JwtService;
use App\Services\TwoFactorService;
use Exception;

/**
 * Authentication Controller
 * Handles user registration, login, logout, and password management
 */
class AuthController extends Controller
{
    private JwtService $jwtService;
    private TwoFactorService $twoFactorService;

    public function __construct(Container $container)
    {
        parent::__construct($container);
        $this->jwtService = $container->get(JwtService::class);
        $this->twoFactorService = $container->get(TwoFactorService::class);
    }

    /**
     * Register new user
     */
    public function register(Request $request): JsonResponse
    {
        try {
            // Rate limiting
            $ip = $request->getClientIp();
            if (!$this->rateLimit("register:{$ip}", 5, 60)) {
                return $this->error('Too many registration attempts. Please try again later.', 429);
            }

            // Validate input
            $data = $this->validate([
                'first_name' => 'required|min:2|max:100',
                'last_name' => 'required|min:2|max:100',
                'email' => 'required|email|unique:users',
                'password' => 'required|min:8|max:255',
                'password_confirmation' => 'required|confirmed',
                'role' => 'required|in:viewer,filmmaker',
                'terms_accepted' => 'required|boolean'
            ]);

            if (!$data['terms_accepted']) {
                return $this->error('You must accept the terms of service');
            }

            // Create user
            $user = User::createUser($this->db, [
                'first_name' => $data['first_name'],
                'last_name' => $data['last_name'],
                'email' => $data['email'],
                'password' => $data['password'],
                'role' => $data['role'],
                'username' => $this->generateUsername($data['first_name'], $data['last_name'])
            ]);

            // Send email verification
            $this->sendEmailVerification($user);

            // Log activity
            $this->logActivity('user_registered', 'User registered successfully', [
                'user_id' => $user->id,
                'email' => $user->email,
                'role' => $user->role
            ]);

            return $this->success([
                'user' => $user->toArray(),
                'message' => 'Registration successful. Please check your email to verify your account.'
            ], 'User registered successfully');

        } catch (Exception $e) {
            $this->logger->error('Registration failed: ' . $e->getMessage());
            return $this->error('Registration failed. Please try again.');
        }
    }

    /**
     * Login user
     */
    public function login(Request $request): JsonResponse
    {
        try {
            // Rate limiting
            $ip = $request->getClientIp();
            if (!$this->rateLimit("login:{$ip}", 10, 60)) {
                return $this->error('Too many login attempts. Please try again later.', 429);
            }

            // Validate input
            $data = $this->validate([
                'email' => 'required|email',
                'password' => 'required',
                'remember_me' => 'boolean'
            ]);

            // Find user
            $user = User::findByEmail($this->db, $data['email']);
            if (!$user || !$user->verifyPassword($data['password'])) {
                return $this->error('Invalid credentials', 401);
            }

            // Check if account is active
            if (!$user->isActive()) {
                return $this->error('Account is not active. Please verify your email.', 401);
            }

            // Check if 2FA is enabled
            if ($user->two_factor_enabled) {
                // Store user ID in session for 2FA verification
                $this->session->set('2fa_user_id', $user->id);
                
                return $this->success([
                    'requires_2fa' => true,
                    'message' => 'Two-factor authentication required'
                ]);
            }

            // Generate tokens
            $tokens = $this->jwtService->createToken($user->toArray());

            // Update last login
            $user->updateLastLogin($request->getClientIp());

            // Log activity
            $this->logActivity('user_login', 'User logged in successfully', [
                'user_id' => $user->id,
                'ip_address' => $request->getClientIp()
            ]);

            return $this->success([
                'user' => $user->toArray(),
                'tokens' => $tokens
            ], 'Login successful');

        } catch (Exception $e) {
            $this->logger->error('Login failed: ' . $e->getMessage());
            return $this->error('Login failed. Please try again.');
        }
    }

    /**
     * Verify 2FA code and complete login
     */
    public function verify2FA(Request $request): JsonResponse
    {
        try {
            // Validate input
            $data = $this->validate([
                'code' => 'required|min:6|max:8'
            ]);

            // Get user from session
            $userId = $this->session->get('2fa_user_id');
            if (!$userId) {
                return $this->error('2FA session expired. Please login again.', 401);
            }

            $user = User::find($this->db, $userId);
            if (!$user) {
                return $this->error('User not found', 404);
            }

            // Verify 2FA code
            if (!$this->twoFactorService->verifyUserCode($userId, $data['code'])) {
                return $this->error('Invalid 2FA code', 401);
            }

            // Clear 2FA session
            $this->session->remove('2fa_user_id');
            
            // Mark 2FA as verified for this session
            $this->session->set("2fa_verified_{$userId}", true);

            // Generate tokens
            $tokens = $this->jwtService->createToken($user->toArray());

            // Update last login
            $user->updateLastLogin($request->getClientIp());

            // Log activity
            $this->logActivity('user_2fa_verified', 'User completed 2FA verification', [
                'user_id' => $user->id,
                'ip_address' => $request->getClientIp()
            ]);

            return $this->success([
                'user' => $user->toArray(),
                'tokens' => $tokens
            ], '2FA verification successful');

        } catch (Exception $e) {
            $this->logger->error('2FA verification failed: ' . $e->getMessage());
            return $this->error('2FA verification failed. Please try again.');
        }
    }

    /**
     * Logout user
     */
    public function logout(Request $request): JsonResponse
    {
        try {
            $user = $this->getUser();
            
            if ($user) {
                // Clear 2FA verification
                $this->session->remove("2fa_verified_{$user['id']}");
                
                // Log activity
                $this->logActivity('user_logout', 'User logged out', [
                    'user_id' => $user['id']
                ]);
            }

            // Clear session
            $this->session->clear();

            return $this->success([], 'Logout successful');

        } catch (Exception $e) {
            $this->logger->error('Logout failed: ' . $e->getMessage());
            return $this->error('Logout failed');
        }
    }

    /**
     * Refresh JWT token
     */
    public function refreshToken(Request $request): JsonResponse
    {
        try {
            $data = $this->validate([
                'refresh_token' => 'required'
            ]);

            $tokens = $this->jwtService->refreshToken($data['refresh_token']);

            return $this->success(['tokens' => $tokens], 'Token refreshed successfully');

        } catch (Exception $e) {
            $this->logger->error('Token refresh failed: ' . $e->getMessage());
            return $this->error('Invalid refresh token', 401);
        }
    }

    /**
     * Send password reset email
     */
    public function forgotPassword(Request $request): JsonResponse
    {
        try {
            // Rate limiting
            $ip = $request->getClientIp();
            if (!$this->rateLimit("forgot_password:{$ip}", 3, 60)) {
                return $this->error('Too many password reset attempts. Please try again later.', 429);
            }

            $data = $this->validate([
                'email' => 'required|email'
            ]);

            $user = User::findByEmail($this->db, $data['email']);
            
            // Always return success to prevent email enumeration
            if ($user) {
                $this->sendPasswordResetEmail($user);
                
                $this->logActivity('password_reset_requested', 'Password reset requested', [
                    'user_id' => $user->id,
                    'email' => $user->email
                ]);
            }

            return $this->success([], 'If an account with that email exists, a password reset link has been sent.');

        } catch (Exception $e) {
            $this->logger->error('Password reset request failed: ' . $e->getMessage());
            return $this->error('Password reset request failed. Please try again.');
        }
    }

    /**
     * Reset password with token
     */
    public function resetPassword(Request $request): JsonResponse
    {
        try {
            $data = $this->validate([
                'token' => 'required',
                'password' => 'required|min:8|max:255',
                'password_confirmation' => 'required|confirmed'
            ]);

            // Verify token
            $payload = $this->jwtService->decode($data['token']);
            
            if ($payload['type'] !== 'password_reset') {
                return $this->error('Invalid reset token', 400);
            }

            $user = User::findByEmail($this->db, $payload['email']);
            if (!$user) {
                return $this->error('User not found', 404);
            }

            // Update password
            $user->updatePassword($data['password']);

            // Log activity
            $this->logActivity('password_reset_completed', 'Password reset completed', [
                'user_id' => $user->id
            ]);

            return $this->success([], 'Password reset successful');

        } catch (Exception $e) {
            $this->logger->error('Password reset failed: ' . $e->getMessage());
            return $this->error('Invalid or expired reset token', 400);
        }
    }

    /**
     * Verify email address
     */
    public function verifyEmail(Request $request): JsonResponse
    {
        try {
            $data = $this->validate([
                'token' => 'required'
            ]);

            // Verify token
            $payload = $this->jwtService->decode($data['token']);
            
            if ($payload['type'] !== 'email_verification') {
                return $this->error('Invalid verification token', 400);
            }

            $user = User::find($this->db, $payload['user_id']);
            if (!$user) {
                return $this->error('User not found', 404);
            }

            if ($user->isEmailVerified()) {
                return $this->success([], 'Email already verified');
            }

            // Mark email as verified
            $user->markEmailAsVerified();

            // Log activity
            $this->logActivity('email_verified', 'Email address verified', [
                'user_id' => $user->id
            ]);

            return $this->success([], 'Email verified successfully');

        } catch (Exception $e) {
            $this->logger->error('Email verification failed: ' . $e->getMessage());
            return $this->error('Invalid or expired verification token', 400);
        }
    }

    /**
     * Generate unique username
     */
    private function generateUsername(string $firstName, string $lastName): string
    {
        $base = strtolower($firstName . $lastName);
        $base = preg_replace('/[^a-z0-9]/', '', $base);
        
        $username = $base;
        $counter = 1;
        
        while (User::findByUsername($this->db, $username)) {
            $username = $base . $counter;
            $counter++;
        }
        
        return $username;
    }

    /**
     * Send email verification
     */
    private function sendEmailVerification(User $user): void
    {
        $token = $this->jwtService->createEmailVerificationToken($user->id, $user->email);
        
        // Store token in database for tracking
        $this->db->insert('email_verification_tokens', [
            'user_id' => $user->id,
            'token' => $token,
            'expires_at' => date('Y-m-d H:i:s', time() + 86400) // 24 hours
        ]);
        
        // TODO: Send actual email
        $this->logger->info("Email verification token for {$user->email}: {$token}");
    }

    /**
     * Send password reset email
     */
    private function sendPasswordResetEmail(User $user): void
    {
        $token = $this->jwtService->createPasswordResetToken($user->email);
        
        // Store token in database for tracking
        $this->db->insert('password_reset_tokens', [
            'email' => $user->email,
            'token' => $token,
            'expires_at' => date('Y-m-d H:i:s', time() + 3600) // 1 hour
        ]);
        
        // TODO: Send actual email
        $this->logger->info("Password reset token for {$user->email}: {$token}");
    }
}
