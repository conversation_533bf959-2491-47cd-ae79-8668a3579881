<?php

declare(strict_types=1);

namespace App\Controllers;

use App\Core\Controller;
use App\Core\Container;
use App\Core\Http\Request;
use App\Core\Http\JsonResponse;
use App\Models\User;
use Exception;

/**
 * Filmmaker Controller
 * Handles filmmaker profile management and verification
 */
class FilmmakerController extends Controller
{
    public function __construct(Container $container)
    {
        parent::__construct($container);
    }

    /**
     * Get filmmaker profile
     */
    public function getProfile(Request $request, string $id): JsonResponse
    {
        try {
            $filmmaker = User::find($this->db, (int)$id);
            if (!$filmmaker || !$filmmaker->isFilmmaker()) {
                return $this->error('Filmmaker not found', 404);
            }

            $profile = $filmmaker->getFilmmakerProfile();
            if (!$profile) {
                return $this->error('Filmmaker profile not found', 404);
            }

            return $this->success([
                'filmmaker' => $filmmaker->toArray(),
                'profile' => $profile
            ], 'Filmmaker profile retrieved successfully');

        } catch (Exception $e) {
            $this->logger->error('Get filmmaker profile failed: ' . $e->getMessage());
            return $this->error('Failed to retrieve filmmaker profile');
        }
    }

    /**
     * Create filmmaker profile
     */
    public function createProfile(Request $request): JsonResponse
    {
        try {
            $this->requireAuth();
            $this->requireRole('filmmaker');
            $user = $this->getUser();

            // Check if profile already exists
            $existingProfile = $this->db->fetchOne(
                "SELECT id FROM filmmaker_profiles WHERE user_id = :user_id",
                ['user_id' => $user['id']]
            );

            if ($existingProfile) {
                return $this->error('Filmmaker profile already exists');
            }

            $data = $this->validate([
                'company_name' => 'max:255',
                'bio' => 'max:2000',
                'website_url' => 'url|max:500',
                'social_media_links' => 'json',
                'specialties' => 'json',
                'years_experience' => 'integer|min:0|max:100',
                'awards_achievements' => 'max:2000',
                'portfolio_links' => 'json'
            ]);

            // Create filmmaker profile
            $profileId = $this->db->insert('filmmaker_profiles', [
                'user_id' => $user['id'],
                'company_name' => $data['company_name'] ?? null,
                'bio' => $data['bio'] ?? null,
                'website_url' => $data['website_url'] ?? null,
                'social_media_links' => isset($data['social_media_links']) ? json_encode($data['social_media_links']) : null,
                'specialties' => isset($data['specialties']) ? json_encode($data['specialties']) : null,
                'years_experience' => $data['years_experience'] ?? null,
                'awards_achievements' => $data['awards_achievements'] ?? null,
                'portfolio_links' => isset($data['portfolio_links']) ? json_encode($data['portfolio_links']) : null,
                'verification_status' => 'pending'
            ]);

            // Log activity
            $this->logActivity('filmmaker_profile_created', 'Filmmaker profile created', [
                'user_id' => $user['id'],
                'profile_id' => $profileId
            ]);

            return $this->success([
                'profile_id' => $profileId,
                'message' => 'Filmmaker profile created successfully. Verification is pending.'
            ], 'Profile created successfully');

        } catch (Exception $e) {
            $this->logger->error('Create filmmaker profile failed: ' . $e->getMessage());
            return $this->error('Failed to create filmmaker profile');
        }
    }

    /**
     * Update filmmaker profile
     */
    public function updateProfile(Request $request): JsonResponse
    {
        try {
            $this->requireAuth();
            $this->requireRole('filmmaker');
            $user = $this->getUser();

            $profile = $this->db->fetchOne(
                "SELECT * FROM filmmaker_profiles WHERE user_id = :user_id",
                ['user_id' => $user['id']]
            );

            if (!$profile) {
                return $this->error('Filmmaker profile not found', 404);
            }

            $data = $this->validate([
                'company_name' => 'max:255',
                'bio' => 'max:2000',
                'website_url' => 'url|max:500',
                'social_media_links' => 'json',
                'specialties' => 'json',
                'years_experience' => 'integer|min:0|max:100',
                'awards_achievements' => 'max:2000',
                'portfolio_links' => 'json'
            ]);

            // Prepare update data
            $updateData = [];
            foreach ($data as $key => $value) {
                if (in_array($key, ['social_media_links', 'specialties', 'portfolio_links']) && is_array($value)) {
                    $updateData[$key] = json_encode($value);
                } else {
                    $updateData[$key] = $value;
                }
            }

            // Update profile
            $this->db->update('filmmaker_profiles', $updateData, ['user_id' => $user['id']]);

            // Log activity
            $this->logActivity('filmmaker_profile_updated', 'Filmmaker profile updated', [
                'user_id' => $user['id'],
                'updated_fields' => array_keys($data)
            ]);

            return $this->success([], 'Filmmaker profile updated successfully');

        } catch (Exception $e) {
            $this->logger->error('Update filmmaker profile failed: ' . $e->getMessage());
            return $this->error('Failed to update filmmaker profile');
        }
    }

    /**
     * Submit verification documents
     */
    public function submitVerification(Request $request): JsonResponse
    {
        try {
            $this->requireAuth();
            $this->requireRole('filmmaker');
            $user = $this->getUser();

            $profile = $this->db->fetchOne(
                "SELECT * FROM filmmaker_profiles WHERE user_id = :user_id",
                ['user_id' => $user['id']]
            );

            if (!$profile) {
                return $this->error('Filmmaker profile not found', 404);
            }

            if ($profile['verification_status'] === 'verified') {
                return $this->error('Profile is already verified');
            }

            // Handle document uploads
            $documents = [];
            $uploadFields = ['id_document', 'business_license', 'portfolio_sample'];

            foreach ($uploadFields as $field) {
                $uploadResult = $this->uploadFile($field, 'verification_documents');
                if ($uploadResult) {
                    $documents[$field] = $uploadResult;
                }
            }

            if (empty($documents)) {
                return $this->error('At least one verification document is required');
            }

            // Update profile with verification documents
            $this->db->update('filmmaker_profiles', [
                'verification_documents' => json_encode($documents),
                'verification_status' => 'pending'
            ], ['user_id' => $user['id']]);

            // Log activity
            $this->logActivity('verification_submitted', 'Verification documents submitted', [
                'user_id' => $user['id'],
                'documents' => array_keys($documents)
            ]);

            return $this->success([], 'Verification documents submitted successfully. Review is pending.');

        } catch (Exception $e) {
            $this->logger->error('Submit verification failed: ' . $e->getMessage());
            return $this->error('Failed to submit verification documents');
        }
    }

    /**
     * Get verification status
     */
    public function getVerificationStatus(Request $request): JsonResponse
    {
        try {
            $this->requireAuth();
            $this->requireRole('filmmaker');
            $user = $this->getUser();

            $profile = $this->db->fetchOne(
                "SELECT verification_status, verified_at FROM filmmaker_profiles WHERE user_id = :user_id",
                ['user_id' => $user['id']]
            );

            if (!$profile) {
                return $this->error('Filmmaker profile not found', 404);
            }

            return $this->success([
                'verification_status' => $profile['verification_status'],
                'verified_at' => $profile['verified_at']
            ], 'Verification status retrieved successfully');

        } catch (Exception $e) {
            $this->logger->error('Get verification status failed: ' . $e->getMessage());
            return $this->error('Failed to retrieve verification status');
        }
    }

    /**
     * List all filmmakers (public endpoint)
     */
    public function listFilmmakers(Request $request): JsonResponse
    {
        try {
            $pagination = $this->getPaginationParams();
            $search = $request->query('search', '');
            $specialty = $request->query('specialty', '');

            $sql = "SELECT u.id, u.first_name, u.last_name, u.username, u.avatar_url,
                           fp.company_name, fp.bio, fp.specialties, fp.years_experience,
                           fp.verification_status
                    FROM users u
                    JOIN filmmaker_profiles fp ON u.id = fp.user_id
                    WHERE u.role = 'filmmaker' AND u.status = 'active'
                    AND fp.verification_status = 'verified'";

            $params = [];

            if ($search) {
                $sql .= " AND (u.first_name LIKE :search OR u.last_name LIKE :search 
                         OR fp.company_name LIKE :search OR fp.bio LIKE :search)";
                $params['search'] = "%{$search}%";
            }

            if ($specialty) {
                $sql .= " AND JSON_CONTAINS(fp.specialties, :specialty)";
                $params['specialty'] = json_encode($specialty);
            }

            $sql .= " ORDER BY fp.verified_at DESC LIMIT :limit OFFSET :offset";
            $params['limit'] = $pagination['perPage'];
            $params['offset'] = $pagination['offset'];

            $filmmakers = $this->db->fetchAll($sql, $params);

            // Get total count
            $countSql = str_replace(
                "SELECT u.id, u.first_name, u.last_name, u.username, u.avatar_url,
                           fp.company_name, fp.bio, fp.specialties, fp.years_experience,
                           fp.verification_status",
                "SELECT COUNT(*)",
                $sql
            );
            $countSql = preg_replace('/ORDER BY.*LIMIT.*OFFSET.*/', '', $countSql);
            
            unset($params['limit'], $params['offset']);
            $total = $this->db->query($countSql, $params)->fetchColumn();

            return $this->paginated($filmmakers, (int)$total, $pagination['page'], $pagination['perPage']);

        } catch (Exception $e) {
            $this->logger->error('List filmmakers failed: ' . $e->getMessage());
            return $this->error('Failed to retrieve filmmakers');
        }
    }

    /**
     * Get filmmaker's platforms
     */
    public function getPlatforms(Request $request): JsonResponse
    {
        try {
            $this->requireAuth();
            $this->requireRole('filmmaker');
            $user = $this->getUser();

            $userModel = User::find($this->db, $user['id']);
            if (!$userModel) {
                return $this->error('User not found', 404);
            }

            $platforms = $userModel->getPlatforms();

            return $this->success([
                'platforms' => $platforms
            ], 'Platforms retrieved successfully');

        } catch (Exception $e) {
            $this->logger->error('Get platforms failed: ' . $e->getMessage());
            return $this->error('Failed to retrieve platforms');
        }
    }
}
