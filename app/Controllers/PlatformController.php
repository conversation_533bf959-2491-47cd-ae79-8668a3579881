<?php

declare(strict_types=1);

namespace App\Controllers;

use App\Core\Controller;
use App\Core\Container;
use App\Core\Http\Request;
use App\Core\Http\JsonResponse;
use App\Models\FilmmakerPlatform;
use App\Models\User;
use Exception;

/**
 * Platform Controller
 * Handles filmmaker platform creation and management
 */
class PlatformController extends Controller
{
    public function __construct(Container $container)
    {
        parent::__construct($container);
    }

    /**
     * List all platforms (public)
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $pagination = $this->getPaginationParams();
            $search = $request->query('search', '');
            $featured = $request->query('featured', '');

            $sql = "SELECT fp.*, u.first_name, u.last_name, u.username,
                           (SELECT COUNT(*) FROM user_subscriptions us 
                            WHERE us.platform_id = fp.id AND us.status = 'active') as subscribers_count
                    FROM filmmaker_platforms fp
                    JOIN users u ON fp.filmmaker_id = u.id
                    WHERE fp.status = 'active'";

            $params = [];

            if ($search) {
                $sql .= " AND (fp.name LIKE :search OR fp.description LIKE :search 
                         OR u.first_name LIKE :search OR u.last_name LIKE :search)";
                $params['search'] = "%{$search}%";
            }

            if ($featured === 'true') {
                $sql .= " AND fp.featured_on_discovery = 1";
            }

            $sql .= " ORDER BY fp.created_at DESC LIMIT :limit OFFSET :offset";
            $params['limit'] = $pagination['perPage'];
            $params['offset'] = $pagination['offset'];

            $platforms = $this->db->fetchAll($sql, $params);

            // Get total count
            $countSql = str_replace(
                "SELECT fp.*, u.first_name, u.last_name, u.username,
                           (SELECT COUNT(*) FROM user_subscriptions us 
                            WHERE us.platform_id = fp.id AND us.status = 'active') as subscribers_count",
                "SELECT COUNT(*)",
                $sql
            );
            $countSql = preg_replace('/ORDER BY.*LIMIT.*OFFSET.*/', '', $countSql);
            
            unset($params['limit'], $params['offset']);
            $total = $this->db->query($countSql, $params)->fetchColumn();

            return $this->paginated($platforms, (int)$total, $pagination['page'], $pagination['perPage']);

        } catch (Exception $e) {
            $this->logger->error('List platforms failed: ' . $e->getMessage());
            return $this->error('Failed to retrieve platforms');
        }
    }

    /**
     * Create new platform
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $this->requireAuth();
            $this->requireRole('filmmaker');
            $user = $this->getUser();

            // Check if filmmaker is verified
            $profile = $this->db->fetchOne(
                "SELECT verification_status FROM filmmaker_profiles WHERE user_id = :user_id",
                ['user_id' => $user['id']]
            );

            if (!$profile || $profile['verification_status'] !== 'verified') {
                return $this->error('Filmmaker verification required to create platforms');
            }

            $data = $this->validate([
                'name' => 'required|min:3|max:255',
                'description' => 'max:2000',
                'tagline' => 'max:500',
                'theme_settings' => 'json',
                'subscription_enabled' => 'boolean',
                'ppv_enabled' => 'boolean',
                'free_content_enabled' => 'boolean'
            ]);

            $data['filmmaker_id'] = $user['id'];

            $platform = FilmmakerPlatform::createPlatform($this->db, $data);

            // Log activity
            $this->logActivity('platform_created', 'Platform created', [
                'platform_id' => $platform->id,
                'platform_name' => $platform->name
            ]);

            return $this->success([
                'platform' => $platform->toArray()
            ], 'Platform created successfully');

        } catch (Exception $e) {
            $this->logger->error('Create platform failed: ' . $e->getMessage());
            return $this->error('Failed to create platform');
        }
    }

    /**
     * Get platform details
     */
    public function show(Request $request, string $id): JsonResponse
    {
        try {
            $platform = FilmmakerPlatform::find($this->db, (int)$id);
            if (!$platform) {
                return $this->error('Platform not found', 404);
            }

            // Get platform owner
            $owner = $platform->getOwner();
            
            // Get recent content
            $recentContent = $platform->getContent([
                'status' => 'published',
                'limit' => 10
            ]);

            // Get analytics (if user owns the platform)
            $analytics = null;
            $user = $this->getUser();
            if ($user && $user['id'] == $platform->filmmaker_id) {
                $analytics = $platform->getAnalytics();
            }

            return $this->success([
                'platform' => $platform->toArray(),
                'owner' => [
                    'id' => $owner['id'],
                    'name' => $owner['first_name'] . ' ' . $owner['last_name'],
                    'username' => $owner['username'],
                    'avatar_url' => $owner['avatar_url']
                ],
                'recent_content' => $recentContent,
                'analytics' => $analytics
            ], 'Platform retrieved successfully');

        } catch (Exception $e) {
            $this->logger->error('Get platform failed: ' . $e->getMessage());
            return $this->error('Failed to retrieve platform');
        }
    }

    /**
     * Update platform
     */
    public function update(Request $request, string $id): JsonResponse
    {
        try {
            $this->requireAuth();
            $user = $this->getUser();

            $platform = FilmmakerPlatform::find($this->db, (int)$id);
            if (!$platform) {
                return $this->error('Platform not found', 404);
            }

            // Check ownership
            if ($platform->filmmaker_id != $user['id']) {
                return $this->error('Access denied', 403);
            }

            $data = $this->validate([
                'name' => 'min:3|max:255',
                'description' => 'max:2000',
                'tagline' => 'max:500',
                'theme_settings' => 'json',
                'branding_settings' => 'json',
                'subscription_enabled' => 'boolean',
                'ppv_enabled' => 'boolean',
                'free_content_enabled' => 'boolean',
                'featured_on_discovery' => 'boolean',
                'social_sharing_enabled' => 'boolean',
                'comments_enabled' => 'boolean',
                'ratings_enabled' => 'boolean',
                'seo_title' => 'max:255',
                'seo_description' => 'max:500',
                'seo_keywords' => 'max:500'
            ]);

            // Update slug if name changed
            if (isset($data['name']) && $data['name'] !== $platform->name) {
                $platform->updateSlug($data['name']);
            }

            $platform->fill($data);
            $platform->save();

            // Log activity
            $this->logActivity('platform_updated', 'Platform updated', [
                'platform_id' => $platform->id,
                'updated_fields' => array_keys($data)
            ]);

            return $this->success([
                'platform' => $platform->toArray()
            ], 'Platform updated successfully');

        } catch (Exception $e) {
            $this->logger->error('Update platform failed: ' . $e->getMessage());
            return $this->error('Failed to update platform');
        }
    }

    /**
     * Delete platform
     */
    public function destroy(Request $request, string $id): JsonResponse
    {
        try {
            $this->requireAuth();
            $user = $this->getUser();

            $platform = FilmmakerPlatform::find($this->db, (int)$id);
            if (!$platform) {
                return $this->error('Platform not found', 404);
            }

            // Check ownership
            if ($platform->filmmaker_id != $user['id']) {
                return $this->error('Access denied', 403);
            }

            // Check if platform has active subscriptions
            $activeSubscriptions = $this->db->query(
                "SELECT COUNT(*) FROM user_subscriptions WHERE platform_id = :platform_id AND status = 'active'",
                ['platform_id' => $platform->id]
            )->fetchColumn();

            if ($activeSubscriptions > 0) {
                return $this->error('Cannot delete platform with active subscriptions');
            }

            $platform->delete();

            // Log activity
            $this->logActivity('platform_deleted', 'Platform deleted', [
                'platform_id' => $platform->id,
                'platform_name' => $platform->name
            ]);

            return $this->success([], 'Platform deleted successfully');

        } catch (Exception $e) {
            $this->logger->error('Delete platform failed: ' . $e->getMessage());
            return $this->error('Failed to delete platform');
        }
    }

    /**
     * Upload platform logo
     */
    public function uploadLogo(Request $request, string $id): JsonResponse
    {
        try {
            $this->requireAuth();
            $user = $this->getUser();

            $platform = FilmmakerPlatform::find($this->db, (int)$id);
            if (!$platform) {
                return $this->error('Platform not found', 404);
            }

            // Check ownership
            if ($platform->filmmaker_id != $user['id']) {
                return $this->error('Access denied', 403);
            }

            $uploadResult = $this->uploadFile('logo', 'platform_logos');
            if (!$uploadResult) {
                return $this->error('Failed to upload logo');
            }

            // Validate file type
            $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
            if (!in_array($uploadResult['type'], $allowedTypes)) {
                return $this->error('Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.');
            }

            $platform->logo_url = $uploadResult['url'];
            $platform->save();

            return $this->success([
                'logo_url' => $platform->logo_url
            ], 'Logo uploaded successfully');

        } catch (Exception $e) {
            $this->logger->error('Upload logo failed: ' . $e->getMessage());
            return $this->error('Failed to upload logo');
        }
    }

    /**
     * Upload platform banner
     */
    public function uploadBanner(Request $request, string $id): JsonResponse
    {
        try {
            $this->requireAuth();
            $user = $this->getUser();

            $platform = FilmmakerPlatform::find($this->db, (int)$id);
            if (!$platform) {
                return $this->error('Platform not found', 404);
            }

            // Check ownership
            if ($platform->filmmaker_id != $user['id']) {
                return $this->error('Access denied', 403);
            }

            $uploadResult = $this->uploadFile('banner', 'platform_banners');
            if (!$uploadResult) {
                return $this->error('Failed to upload banner');
            }

            // Validate file type
            $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
            if (!in_array($uploadResult['type'], $allowedTypes)) {
                return $this->error('Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.');
            }

            $platform->banner_url = $uploadResult['url'];
            $platform->save();

            return $this->success([
                'banner_url' => $platform->banner_url
            ], 'Banner uploaded successfully');

        } catch (Exception $e) {
            $this->logger->error('Upload banner failed: ' . $e->getMessage());
            return $this->error('Failed to upload banner');
        }
    }

    /**
     * Get platform analytics
     */
    public function getAnalytics(Request $request, string $id): JsonResponse
    {
        try {
            $this->requireAuth();
            $user = $this->getUser();

            $platform = FilmmakerPlatform::find($this->db, (int)$id);
            if (!$platform) {
                return $this->error('Platform not found', 404);
            }

            // Check ownership
            if ($platform->filmmaker_id != $user['id']) {
                return $this->error('Access denied', 403);
            }

            $period = $request->query('period', '30d');
            $analytics = $platform->getAnalytics($period);

            return $this->success([
                'analytics' => $analytics,
                'period' => $period
            ], 'Analytics retrieved successfully');

        } catch (Exception $e) {
            $this->logger->error('Get analytics failed: ' . $e->getMessage());
            return $this->error('Failed to retrieve analytics');
        }
    }
}
