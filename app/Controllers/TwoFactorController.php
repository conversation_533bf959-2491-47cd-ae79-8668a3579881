<?php

declare(strict_types=1);

namespace App\Controllers;

use App\Core\Controller;
use App\Core\Container;
use App\Core\Http\Request;
use App\Core\Http\JsonResponse;
use App\Models\User;
use App\Services\TwoFactorService;
use Exception;

/**
 * Two-Factor Authentication Controller
 * Handles 2FA setup, verification, and management
 */
class TwoFactorController extends Controller
{
    private TwoFactorService $twoFactorService;

    public function __construct(Container $container)
    {
        parent::__construct($container);
        $this->twoFactorService = $container->get(TwoFactorService::class);
    }

    /**
     * Get 2FA setup data
     */
    public function getSetupData(Request $request): JsonResponse
    {
        try {
            $this->requireAuth();
            $user = $this->getUser();

            if ($user['two_factor_enabled']) {
                return $this->error('2FA is already enabled for this account');
            }

            $setupData = $this->twoFactorService->getSetupData($user['email']);

            return $this->success([
                'secret' => $setupData['secret'],
                'qr_code' => $setupData['qr_code'],
                'manual_entry_key' => $setupData['manual_entry_key'],
                'issuer' => $this->config->get('two_fa.issuer', 'Filmmaker Netflix')
            ], '2FA setup data generated');

        } catch (Exception $e) {
            $this->logger->error('2FA setup data generation failed: ' . $e->getMessage());
            return $this->error('Failed to generate 2FA setup data');
        }
    }

    /**
     * Enable 2FA for user
     */
    public function enable(Request $request): JsonResponse
    {
        try {
            $this->requireAuth();
            $user = $this->getUser();

            if ($user['two_factor_enabled']) {
                return $this->error('2FA is already enabled for this account');
            }

            $data = $this->validate([
                'secret' => 'required',
                'code' => 'required|min:6|max:6'
            ]);

            // Enable 2FA
            if (!$this->twoFactorService->enableTwoFactor($user['id'], $data['secret'], $data['code'])) {
                return $this->error('Invalid 2FA code. Please try again.');
            }

            // Generate recovery codes
            $recoveryCodes = $this->twoFactorService->getBackupCodes($user['id']);

            // Log activity
            $this->logActivity('2fa_enabled', '2FA enabled for user', [
                'user_id' => $user['id']
            ]);

            return $this->success([
                'recovery_codes' => $recoveryCodes,
                'message' => '2FA enabled successfully. Please save your recovery codes in a safe place.'
            ], '2FA enabled successfully');

        } catch (Exception $e) {
            $this->logger->error('2FA enable failed: ' . $e->getMessage());
            return $this->error('Failed to enable 2FA. Please try again.');
        }
    }

    /**
     * Disable 2FA for user
     */
    public function disable(Request $request): JsonResponse
    {
        try {
            $this->requireAuth();
            $user = $this->getUser();

            if (!$user['two_factor_enabled']) {
                return $this->error('2FA is not enabled for this account');
            }

            $data = $this->validate([
                'code' => 'required|min:6|max:8',
                'password' => 'required'
            ]);

            // Verify password
            $userModel = User::find($this->db, $user['id']);
            if (!$userModel || !$userModel->verifyPassword($data['password'])) {
                return $this->error('Invalid password');
            }

            // Verify 2FA code
            if (!$this->twoFactorService->verifyUserCode($user['id'], $data['code'])) {
                return $this->error('Invalid 2FA code');
            }

            // Disable 2FA
            $this->twoFactorService->disableTwoFactor($user['id']);

            // Clear 2FA verification from session
            $this->session->remove("2fa_verified_{$user['id']}");

            // Log activity
            $this->logActivity('2fa_disabled', '2FA disabled for user', [
                'user_id' => $user['id']
            ]);

            return $this->success([], '2FA disabled successfully');

        } catch (Exception $e) {
            $this->logger->error('2FA disable failed: ' . $e->getMessage());
            return $this->error('Failed to disable 2FA. Please try again.');
        }
    }

    /**
     * Verify 2FA code (for session verification)
     */
    public function verify(Request $request): JsonResponse
    {
        try {
            $this->requireAuth();
            $user = $this->getUser();

            if (!$user['two_factor_enabled']) {
                return $this->error('2FA is not enabled for this account');
            }

            $data = $this->validate([
                'code' => 'required|min:6|max:8'
            ]);

            // Verify 2FA code
            if (!$this->twoFactorService->verifyUserCode($user['id'], $data['code'])) {
                return $this->error('Invalid 2FA code');
            }

            // Mark 2FA as verified for this session
            $this->session->set("2fa_verified_{$user['id']}", true);

            // Log activity
            $this->logActivity('2fa_verified', '2FA code verified', [
                'user_id' => $user['id']
            ]);

            return $this->success([], '2FA verification successful');

        } catch (Exception $e) {
            $this->logger->error('2FA verification failed: ' . $e->getMessage());
            return $this->error('2FA verification failed. Please try again.');
        }
    }

    /**
     * Get recovery codes
     */
    public function getRecoveryCodes(Request $request): JsonResponse
    {
        try {
            $this->requireAuth();
            $user = $this->getUser();

            if (!$user['two_factor_enabled']) {
                return $this->error('2FA is not enabled for this account');
            }

            // Require 2FA verification for this sensitive operation
            if (!$this->session->get("2fa_verified_{$user['id']}")) {
                return $this->error('2FA verification required for this operation', 403);
            }

            $recoveryCodes = $this->twoFactorService->getBackupCodes($user['id']);
            $remainingCount = count($recoveryCodes);

            return $this->success([
                'recovery_codes' => $recoveryCodes,
                'remaining_count' => $remainingCount
            ], 'Recovery codes retrieved');

        } catch (Exception $e) {
            $this->logger->error('Recovery codes retrieval failed: ' . $e->getMessage());
            return $this->error('Failed to retrieve recovery codes');
        }
    }

    /**
     * Regenerate recovery codes
     */
    public function regenerateRecoveryCodes(Request $request): JsonResponse
    {
        try {
            $this->requireAuth();
            $user = $this->getUser();

            if (!$user['two_factor_enabled']) {
                return $this->error('2FA is not enabled for this account');
            }

            $data = $this->validate([
                'code' => 'required|min:6|max:8'
            ]);

            // Verify 2FA code
            if (!$this->twoFactorService->verifyUserCode($user['id'], $data['code'])) {
                return $this->error('Invalid 2FA code');
            }

            // Regenerate recovery codes
            $recoveryCodes = $this->twoFactorService->regenerateRecoveryCodes($user['id']);

            // Log activity
            $this->logActivity('2fa_recovery_codes_regenerated', 'Recovery codes regenerated', [
                'user_id' => $user['id']
            ]);

            return $this->success([
                'recovery_codes' => $recoveryCodes,
                'message' => 'New recovery codes generated. Please save them in a safe place.'
            ], 'Recovery codes regenerated successfully');

        } catch (Exception $e) {
            $this->logger->error('Recovery codes regeneration failed: ' . $e->getMessage());
            return $this->error('Failed to regenerate recovery codes');
        }
    }

    /**
     * Get 2FA status
     */
    public function getStatus(Request $request): JsonResponse
    {
        try {
            $this->requireAuth();
            $user = $this->getUser();

            $status = [
                'enabled' => $user['two_factor_enabled'],
                'verified_for_session' => $this->session->get("2fa_verified_{$user['id']}", false)
            ];

            if ($user['two_factor_enabled']) {
                $status['remaining_recovery_codes'] = $this->twoFactorService->getRemainingRecoveryCodesCount($user['id']);
            }

            return $this->success($status, '2FA status retrieved');

        } catch (Exception $e) {
            $this->logger->error('2FA status retrieval failed: ' . $e->getMessage());
            return $this->error('Failed to retrieve 2FA status');
        }
    }

    /**
     * Test 2FA setup (before enabling)
     */
    public function testSetup(Request $request): JsonResponse
    {
        try {
            $this->requireAuth();
            $user = $this->getUser();

            if ($user['two_factor_enabled']) {
                return $this->error('2FA is already enabled for this account');
            }

            $data = $this->validate([
                'secret' => 'required',
                'code1' => 'required|min:6|max:6',
                'code2' => 'required|min:6|max:6'
            ]);

            // Test the setup with two consecutive codes
            $isValid = $this->twoFactorService->validateSetup(
                $data['secret'],
                $data['code1'],
                $data['code2']
            );

            if (!$isValid) {
                return $this->error('2FA setup test failed. Please check your authenticator app.');
            }

            return $this->success([], '2FA setup test successful');

        } catch (Exception $e) {
            $this->logger->error('2FA setup test failed: ' . $e->getMessage());
            return $this->error('2FA setup test failed. Please try again.');
        }
    }
}
