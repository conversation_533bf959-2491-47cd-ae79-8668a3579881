<?php

declare(strict_types=1);

namespace App\Controllers;

use App\Core\Controller;
use App\Core\Container;
use App\Core\Http\Request;
use App\Core\Http\JsonResponse;
use App\Models\User;
use Exception;

/**
 * User Controller
 * Handles user profile management and user-related operations
 */
class UserController extends Controller
{
    public function __construct(Container $container)
    {
        parent::__construct($container);
    }

    /**
     * Get current user profile
     */
    public function getProfile(Request $request): JsonResponse
    {
        try {
            $this->requireAuth();
            $user = $this->getUser();
            
            $userModel = User::find($this->db, $user['id']);
            if (!$userModel) {
                return $this->error('User not found', 404);
            }

            return $this->success([
                'user' => $userModel->toArray()
            ], 'Profile retrieved successfully');

        } catch (Exception $e) {
            $this->logger->error('Get profile failed: ' . $e->getMessage());
            return $this->error('Failed to retrieve profile');
        }
    }

    /**
     * Update user profile
     */
    public function updateProfile(Request $request): JsonResponse
    {
        try {
            $this->requireAuth();
            $user = $this->getUser();

            $data = $this->validate([
                'first_name' => 'required|min:2|max:100',
                'last_name' => 'required|min:2|max:100',
                'username' => 'min:3|max:50',
                'phone' => 'max:20',
                'date_of_birth' => 'date',
                'gender' => 'in:male,female,other,prefer_not_to_say',
                'country_code' => 'max:2',
                'timezone' => 'max:50',
                'language_code' => 'max:2',
                'email_notifications' => 'boolean',
                'marketing_notifications' => 'boolean'
            ]);

            $userModel = User::find($this->db, $user['id']);
            if (!$userModel) {
                return $this->error('User not found', 404);
            }

            // Check if username is unique (if provided and different)
            if (isset($data['username']) && $data['username'] !== $userModel->username) {
                $existingUser = User::findByUsername($this->db, $data['username']);
                if ($existingUser && $existingUser->id !== $userModel->id) {
                    return $this->error('Username is already taken');
                }
            }

            // Update user
            $userModel->fill($data);
            $userModel->save();

            // Log activity
            $this->logActivity('profile_updated', 'User profile updated', [
                'user_id' => $userModel->id,
                'updated_fields' => array_keys($data)
            ]);

            return $this->success([
                'user' => $userModel->toArray()
            ], 'Profile updated successfully');

        } catch (Exception $e) {
            $this->logger->error('Update profile failed: ' . $e->getMessage());
            return $this->error('Failed to update profile');
        }
    }

    /**
     * Change password
     */
    public function changePassword(Request $request): JsonResponse
    {
        try {
            $this->requireAuth();
            $user = $this->getUser();

            $data = $this->validate([
                'current_password' => 'required',
                'new_password' => 'required|min:8|max:255',
                'new_password_confirmation' => 'required|confirmed'
            ]);

            $userModel = User::find($this->db, $user['id']);
            if (!$userModel) {
                return $this->error('User not found', 404);
            }

            // Verify current password
            if (!$userModel->verifyPassword($data['current_password'])) {
                return $this->error('Current password is incorrect');
            }

            // Update password
            $userModel->updatePassword($data['new_password']);

            // Log activity
            $this->logActivity('password_changed', 'User password changed', [
                'user_id' => $userModel->id
            ]);

            return $this->success([], 'Password changed successfully');

        } catch (Exception $e) {
            $this->logger->error('Change password failed: ' . $e->getMessage());
            return $this->error('Failed to change password');
        }
    }

    /**
     * Upload avatar
     */
    public function uploadAvatar(Request $request): JsonResponse
    {
        try {
            $this->requireAuth();
            $user = $this->getUser();

            $userModel = User::find($this->db, $user['id']);
            if (!$userModel) {
                return $this->error('User not found', 404);
            }

            // Upload file
            $uploadResult = $this->uploadFile('avatar', 'avatars');
            if (!$uploadResult) {
                return $this->error('Failed to upload avatar');
            }

            // Validate file type
            $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
            if (!in_array($uploadResult['type'], $allowedTypes)) {
                return $this->error('Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.');
            }

            // Validate file size (max 5MB)
            if ($uploadResult['size'] > 5 * 1024 * 1024) {
                return $this->error('File too large. Maximum size is 5MB.');
            }

            // Update user avatar
            $userModel->avatar_url = $uploadResult['url'];
            $userModel->save();

            // Log activity
            $this->logActivity('avatar_uploaded', 'User avatar uploaded', [
                'user_id' => $userModel->id,
                'filename' => $uploadResult['filename']
            ]);

            return $this->success([
                'avatar_url' => $userModel->avatar_url
            ], 'Avatar uploaded successfully');

        } catch (Exception $e) {
            $this->logger->error('Upload avatar failed: ' . $e->getMessage());
            return $this->error('Failed to upload avatar');
        }
    }

    /**
     * Delete account
     */
    public function deleteAccount(Request $request): JsonResponse
    {
        try {
            $this->requireAuth();
            $user = $this->getUser();

            $data = $this->validate([
                'password' => 'required',
                'confirmation' => 'required'
            ]);

            if ($data['confirmation'] !== 'DELETE') {
                return $this->error('Please type "DELETE" to confirm account deletion');
            }

            $userModel = User::find($this->db, $user['id']);
            if (!$userModel) {
                return $this->error('User not found', 404);
            }

            // Verify password
            if (!$userModel->verifyPassword($data['password'])) {
                return $this->error('Password is incorrect');
            }

            // Soft delete user
            $userModel->softDelete();

            // Log activity
            $this->logActivity('account_deleted', 'User account deleted', [
                'user_id' => $userModel->id
            ]);

            // Clear session
            $this->session->clear();

            return $this->success([], 'Account deleted successfully');

        } catch (Exception $e) {
            $this->logger->error('Delete account failed: ' . $e->getMessage());
            return $this->error('Failed to delete account');
        }
    }

    /**
     * Get user's activity log
     */
    public function getActivityLog(Request $request): JsonResponse
    {
        try {
            $this->requireAuth();
            $user = $this->getUser();

            $pagination = $this->getPaginationParams();

            $sql = "SELECT * FROM activity_logs 
                    WHERE user_id = :user_id 
                    ORDER BY created_at DESC 
                    LIMIT :limit OFFSET :offset";

            $activities = $this->db->fetchAll($sql, [
                'user_id' => $user['id'],
                'limit' => $pagination['perPage'],
                'offset' => $pagination['offset']
            ]);

            // Get total count
            $countSql = "SELECT COUNT(*) FROM activity_logs WHERE user_id = :user_id";
            $total = $this->db->query($countSql, ['user_id' => $user['id']])->fetchColumn();

            return $this->paginated($activities, (int)$total, $pagination['page'], $pagination['perPage']);

        } catch (Exception $e) {
            $this->logger->error('Get activity log failed: ' . $e->getMessage());
            return $this->error('Failed to retrieve activity log');
        }
    }

    /**
     * Get user's subscriptions
     */
    public function getSubscriptions(Request $request): JsonResponse
    {
        try {
            $this->requireAuth();
            $user = $this->getUser();

            $userModel = User::find($this->db, $user['id']);
            if (!$userModel) {
                return $this->error('User not found', 404);
            }

            $subscriptions = $userModel->getActiveSubscriptions();

            return $this->success([
                'subscriptions' => $subscriptions
            ], 'Subscriptions retrieved successfully');

        } catch (Exception $e) {
            $this->logger->error('Get subscriptions failed: ' . $e->getMessage());
            return $this->error('Failed to retrieve subscriptions');
        }
    }

    /**
     * Get user's watch history
     */
    public function getWatchHistory(Request $request): JsonResponse
    {
        try {
            $this->requireAuth();
            $user = $this->getUser();

            $pagination = $this->getPaginationParams();

            $sql = "SELECT wh.*, c.title, c.poster_url, c.duration_seconds,
                           p.name as platform_name, p.slug as platform_slug
                    FROM user_watch_history wh
                    JOIN content c ON wh.content_id = c.id
                    JOIN filmmaker_platforms p ON c.platform_id = p.id
                    WHERE wh.user_id = :user_id
                    ORDER BY wh.last_watched_at DESC
                    LIMIT :limit OFFSET :offset";

            $history = $this->db->fetchAll($sql, [
                'user_id' => $user['id'],
                'limit' => $pagination['perPage'],
                'offset' => $pagination['offset']
            ]);

            // Get total count
            $countSql = "SELECT COUNT(*) FROM user_watch_history WHERE user_id = :user_id";
            $total = $this->db->query($countSql, ['user_id' => $user['id']])->fetchColumn();

            return $this->paginated($history, (int)$total, $pagination['page'], $pagination['perPage']);

        } catch (Exception $e) {
            $this->logger->error('Get watch history failed: ' . $e->getMessage());
            return $this->error('Failed to retrieve watch history');
        }
    }
}
