<?php

declare(strict_types=1);

namespace App\Core;

/**
 * Configuration management class
 * Handles loading and accessing configuration values
 */
class Config
{
    private array $config = [];

    public function __construct()
    {
        $this->loadConfiguration();
    }

    /**
     * Load configuration from environment and config files
     */
    private function loadConfiguration(): void
    {
        // Load from environment variables
        $this->config = [
            'app' => [
                'name' => $_ENV['APP_NAME'] ?? 'FilmStream',
                'env' => $_ENV['APP_ENV'] ?? 'production',
                'debug' => filter_var($_ENV['APP_DEBUG'] ?? false, FILTER_VALIDATE_BOOLEAN),
                'url' => $_ENV['APP_URL'] ?? 'http://localhost:8080',
                'key' => $_ENV['APP_KEY'] ?? '',
                'timezone' => $_ENV['APP_TIMEZONE'] ?? 'UTC',
            ],
            'database' => [
                'connection' => $_ENV['DB_CONNECTION'] ?? 'mysql',
                'host' => $_ENV['DB_HOST'] ?? '127.0.0.1',
                'port' => (int)($_ENV['DB_PORT'] ?? 3306),
                'database' => $_ENV['DB_DATABASE'] ?? 'filmmaker_ott',
                'username' => $_ENV['DB_USERNAME'] ?? 'root',
                'password' => $_ENV['DB_PASSWORD'] ?? 'soadrock12',
                'charset' => $_ENV['DB_CHARSET'] ?? 'utf8mb4',
                'collation' => $_ENV['DB_COLLATION'] ?? 'utf8mb4_unicode_ci',
                'options' => [
                    \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                    \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
                    \PDO::ATTR_EMULATE_PREPARES => false,
                ],
            ],
            'jwt' => [
                'secret' => $_ENV['JWT_SECRET'] ?? '',
                'expiry' => (int)($_ENV['JWT_EXPIRY'] ?? 3600),
                'refresh_expiry' => (int)($_ENV['JWT_REFRESH_EXPIRY'] ?? 604800),
                'algorithm' => $_ENV['JWT_ALGORITHM'] ?? 'HS256',
            ],
            'stripe' => [
                'publishable_key' => $_ENV['STRIPE_PUBLISHABLE_KEY'] ?? '',
                'secret_key' => $_ENV['STRIPE_SECRET_KEY'] ?? '',
                'webhook_secret' => $_ENV['STRIPE_WEBHOOK_SECRET'] ?? '',
                'connect_client_id' => $_ENV['STRIPE_CONNECT_CLIENT_ID'] ?? '',
            ],
            'bunny' => [
                'stream_api_key' => $_ENV['BUNNY_STREAM_API_KEY'] ?? '',
                'library_id' => $_ENV['BUNNY_STREAM_LIBRARY_ID'] ?? '',
                'hostname' => $_ENV['BUNNY_STREAM_HOSTNAME'] ?? '',
                'cdn_hostname' => $_ENV['BUNNY_STREAM_CDN_HOSTNAME'] ?? '',
            ],
            'mail' => [
                'mailer' => $_ENV['MAIL_MAILER'] ?? 'smtp',
                'host' => $_ENV['MAIL_HOST'] ?? 'localhost',
                'port' => (int)($_ENV['MAIL_PORT'] ?? 587),
                'username' => $_ENV['MAIL_USERNAME'] ?? '',
                'password' => $_ENV['MAIL_PASSWORD'] ?? '',
                'encryption' => $_ENV['MAIL_ENCRYPTION'] ?? 'tls',
                'from_address' => $_ENV['MAIL_FROM_ADDRESS'] ?? '<EMAIL>',
                'from_name' => $_ENV['MAIL_FROM_NAME'] ?? 'Filmmaker Netflix',
            ],
            'cache' => [
                'driver' => $_ENV['CACHE_DRIVER'] ?? 'file',
                'path' => STORAGE_PATH . '/cache',
            ],
            'session' => [
                'driver' => $_ENV['SESSION_DRIVER'] ?? 'file',
                'lifetime' => (int)($_ENV['SESSION_LIFETIME'] ?? 120),
                'path' => STORAGE_PATH . '/sessions',
                'cookie_name' => 'filmmaker_netflix_session',
                'cookie_secure' => filter_var($_ENV['SESSION_SECURE_COOKIE'] ?? false, FILTER_VALIDATE_BOOLEAN),
                'cookie_httponly' => true,
                'cookie_samesite' => 'Lax',
            ],
            'security' => [
                'bcrypt_rounds' => (int)($_ENV['BCRYPT_ROUNDS'] ?? 12),
                'encryption_key' => $_ENV['ENCRYPTION_KEY'] ?? '',
            ],
            'platform' => [
                'commission_rate' => (float)($_ENV['PLATFORM_COMMISSION_RATE'] ?? 0.15),
                'default_currency' => $_ENV['DEFAULT_CURRENCY'] ?? 'USD',
                'supported_currencies' => explode(',', $_ENV['SUPPORTED_CURRENCIES'] ?? 'USD,EUR,GBP,CAD,AUD'),
            ],
            'video' => [
                'max_size' => (int)($_ENV['MAX_VIDEO_SIZE'] ?? 5368709120), // 5GB
                'allowed_formats' => explode(',', $_ENV['ALLOWED_VIDEO_FORMATS'] ?? 'mp4,mov,avi,mkv,webm'),
                'quality_levels' => explode(',', $_ENV['VIDEO_QUALITY_LEVELS'] ?? '360p,480p,720p,1080p,4k'),
            ],
            'two_fa' => [
                'issuer' => $_ENV['TWO_FA_ISSUER'] ?? 'Filmmaker Netflix',
                'digits' => (int)($_ENV['TWO_FA_DIGITS'] ?? 6),
                'period' => (int)($_ENV['TWO_FA_PERIOD'] ?? 30),
            ],
            'rate_limit' => [
                'requests' => (int)($_ENV['RATE_LIMIT_REQUESTS'] ?? 60),
                'window' => (int)($_ENV['RATE_LIMIT_WINDOW'] ?? 60),
            ],
            'logging' => [
                'channel' => $_ENV['LOG_CHANNEL'] ?? 'stack',
                'level' => $_ENV['LOG_LEVEL'] ?? 'debug',
                'path' => STORAGE_PATH . '/logs',
            ],
        ];

        // Load additional config files if they exist
        $this->loadConfigFiles();
    }

    /**
     * Load configuration files from the config directory
     */
    private function loadConfigFiles(): void
    {
        $configPath = CONFIG_PATH;
        
        if (!is_dir($configPath)) {
            return;
        }

        $files = glob($configPath . '/*.php');
        
        foreach ($files as $file) {
            $key = basename($file, '.php');
            $config = require $file;
            
            if (is_array($config)) {
                $this->config[$key] = array_merge($this->config[$key] ?? [], $config);
            }
        }
    }

    /**
     * Get a configuration value using dot notation
     */
    public function get(string $key, mixed $default = null): mixed
    {
        $keys = explode('.', $key);
        $value = $this->config;

        foreach ($keys as $segment) {
            if (!is_array($value) || !array_key_exists($segment, $value)) {
                return $default;
            }
            $value = $value[$segment];
        }

        return $value;
    }

    /**
     * Set a configuration value using dot notation
     */
    public function set(string $key, mixed $value): void
    {
        $keys = explode('.', $key);
        $config = &$this->config;

        foreach ($keys as $segment) {
            if (!isset($config[$segment]) || !is_array($config[$segment])) {
                $config[$segment] = [];
            }
            $config = &$config[$segment];
        }

        $config = $value;
    }

    /**
     * Check if a configuration key exists
     */
    public function has(string $key): bool
    {
        $keys = explode('.', $key);
        $value = $this->config;

        foreach ($keys as $segment) {
            if (!is_array($value) || !array_key_exists($segment, $value)) {
                return false;
            }
            $value = $value[$segment];
        }

        return true;
    }

    /**
     * Get all configuration
     */
    public function all(): array
    {
        return $this->config;
    }
}
