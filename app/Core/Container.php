<?php

declare(strict_types=1);

namespace App\Core;

use Exception;
use ReflectionClass;
use ReflectionParameter;

/**
 * Dependency Injection Container
 * Manages service registration and resolution
 */
class Container
{
    private array $bindings = [];
    private array $instances = [];
    private array $singletons = [];

    /**
     * Bind a service to the container
     */
    public function bind(string $abstract, $concrete = null): void
    {
        if ($concrete === null) {
            $concrete = $abstract;
        }

        $this->bindings[$abstract] = $concrete;
    }

    /**
     * Bind a singleton service to the container
     */
    public function singleton(string $abstract, $concrete = null): void
    {
        $this->bind($abstract, $concrete);
        $this->singletons[$abstract] = true;
    }

    /**
     * Register an existing instance as a singleton
     */
    public function instance(string $abstract, object $instance): void
    {
        $this->instances[$abstract] = $instance;
        $this->singletons[$abstract] = true;
    }

    /**
     * Resolve a service from the container
     */
    public function get(string $abstract): mixed
    {
        // Return existing instance if it's a singleton
        if (isset($this->instances[$abstract])) {
            return $this->instances[$abstract];
        }

        // Get the concrete implementation
        $concrete = $this->bindings[$abstract] ?? $abstract;

        // Build the instance
        $instance = $this->build($concrete);

        // Store as singleton if needed
        if (isset($this->singletons[$abstract])) {
            $this->instances[$abstract] = $instance;
        }

        return $instance;
    }

    /**
     * Build an instance of the given concrete
     */
    private function build($concrete): mixed
    {
        // If it's a callable, execute it
        if (is_callable($concrete)) {
            return $concrete($this);
        }

        // If it's a string, resolve the class
        if (is_string($concrete)) {
            return $this->buildClass($concrete);
        }

        throw new Exception("Cannot build instance for: " . gettype($concrete));
    }

    /**
     * Build a class instance with dependency injection
     */
    private function buildClass(string $className): object
    {
        if (!class_exists($className)) {
            throw new Exception("Class {$className} does not exist");
        }

        $reflection = new ReflectionClass($className);

        if (!$reflection->isInstantiable()) {
            throw new Exception("Class {$className} is not instantiable");
        }

        $constructor = $reflection->getConstructor();

        // If no constructor, just create the instance
        if ($constructor === null) {
            return new $className();
        }

        // Resolve constructor dependencies
        $dependencies = $this->resolveDependencies($constructor->getParameters());

        return $reflection->newInstanceArgs($dependencies);
    }

    /**
     * Resolve method dependencies
     */
    private function resolveDependencies(array $parameters): array
    {
        $dependencies = [];

        foreach ($parameters as $parameter) {
            $dependency = $this->resolveDependency($parameter);
            $dependencies[] = $dependency;
        }

        return $dependencies;
    }

    /**
     * Resolve a single dependency
     */
    private function resolveDependency(ReflectionParameter $parameter): mixed
    {
        $type = $parameter->getType();

        // If no type hint, check for default value
        if ($type === null) {
            if ($parameter->isDefaultValueAvailable()) {
                return $parameter->getDefaultValue();
            }

            throw new Exception("Cannot resolve parameter {$parameter->getName()} without type hint");
        }

        // Get the type name
        $typeName = $type->getName();

        // If it's a built-in type, check for default value
        if ($type->isBuiltin()) {
            if ($parameter->isDefaultValueAvailable()) {
                return $parameter->getDefaultValue();
            }

            throw new Exception("Cannot resolve built-in type {$typeName} for parameter {$parameter->getName()}");
        }

        // Resolve the class from the container
        try {
            return $this->get($typeName);
        } catch (Exception $e) {
            if ($parameter->isDefaultValueAvailable()) {
                return $parameter->getDefaultValue();
            }

            if ($parameter->allowsNull()) {
                return null;
            }

            throw new Exception("Cannot resolve dependency {$typeName} for parameter {$parameter->getName()}: " . $e->getMessage());
        }
    }

    /**
     * Check if a service is bound
     */
    public function has(string $abstract): bool
    {
        return isset($this->bindings[$abstract]) || isset($this->instances[$abstract]);
    }

    /**
     * Call a method with dependency injection
     */
    public function call($callback, array $parameters = []): mixed
    {
        if (is_array($callback)) {
            [$class, $method] = $callback;
            
            if (is_string($class)) {
                $class = $this->get($class);
            }

            $reflection = new ReflectionClass($class);
            $methodReflection = $reflection->getMethod($method);
            
            $dependencies = $this->resolveDependencies($methodReflection->getParameters());
            $dependencies = array_merge($dependencies, $parameters);
            
            return $methodReflection->invokeArgs($class, $dependencies);
        }

        return $callback(...$parameters);
    }
}
