<?php

declare(strict_types=1);

namespace App\Core;

use App\Core\Http\Request;
use App\Core\Http\Response;
use App\Core\Http\JsonResponse;

/**
 * Base Controller class
 * Provides common functionality for all controllers
 */
abstract class Controller
{
    protected Container $container;
    protected Request $request;
    protected Database $db;
    protected Config $config;
    protected Logger $logger;
    protected Session $session;
    protected Cache $cache;

    public function __construct(Container $container)
    {
        $this->container = $container;
        $this->db = $container->get(Database::class);
        $this->config = $container->get(Config::class);
        $this->logger = $container->get(Logger::class);
        $this->session = $container->get(Session::class);
        $this->cache = $container->get(Cache::class);
    }

    /**
     * Get the current request
     */
    protected function getRequest(): Request
    {
        if (!isset($this->request)) {
            $this->request = $this->container->get(Request::class);
        }
        return $this->request;
    }

    /**
     * Validate request data
     */
    protected function validate(array $rules, ?array $data = null): array
    {
        $data = $data ?? $this->getRequest()->getBody();
        $errors = [];
        
        foreach ($rules as $field => $rule) {
            $value = $data[$field] ?? null;
            $ruleList = is_string($rule) ? explode('|', $rule) : $rule;
            
            foreach ($ruleList as $singleRule) {
                $error = $this->validateField($field, $value, $singleRule, $data);
                if ($error) {
                    $errors[$field][] = $error;
                }
            }
        }
        
        if (!empty($errors)) {
            $this->validationFailed($errors);
        }
        
        return $data;
    }

    /**
     * Validate single field
     */
    protected function validateField(string $field, mixed $value, string $rule, array $data): ?string
    {
        $parts = explode(':', $rule, 2);
        $ruleName = $parts[0];
        $parameter = $parts[1] ?? null;
        
        return match ($ruleName) {
            'required' => empty($value) ? "The {$field} field is required." : null,
            'email' => !filter_var($value, FILTER_VALIDATE_EMAIL) ? "The {$field} must be a valid email address." : null,
            'min' => strlen((string)$value) < (int)$parameter ? "The {$field} must be at least {$parameter} characters." : null,
            'max' => strlen((string)$value) > (int)$parameter ? "The {$field} may not be greater than {$parameter} characters." : null,
            'numeric' => !is_numeric($value) ? "The {$field} must be a number." : null,
            'integer' => !filter_var($value, FILTER_VALIDATE_INT) ? "The {$field} must be an integer." : null,
            'boolean' => !is_bool($value) && !in_array($value, [0, 1, '0', '1', 'true', 'false']) ? "The {$field} must be true or false." : null,
            'url' => !filter_var($value, FILTER_VALIDATE_URL) ? "The {$field} must be a valid URL." : null,
            'unique' => $this->validateUnique($field, $value, $parameter) ? null : "The {$field} has already been taken.",
            'exists' => $this->validateExists($field, $value, $parameter) ? null : "The selected {$field} is invalid.",
            'confirmed' => $value !== ($data[$field . '_confirmation'] ?? null) ? "The {$field} confirmation does not match." : null,
            default => null
        };
    }

    /**
     * Validate unique field
     */
    protected function validateUnique(string $field, mixed $value, ?string $table): bool
    {
        if (!$table || !$value) {
            return true;
        }
        
        $sql = "SELECT COUNT(*) FROM {$table} WHERE {$field} = :value";
        $count = $this->db->query($sql, ['value' => $value])->fetchColumn();
        
        return $count == 0;
    }

    /**
     * Validate exists field
     */
    protected function validateExists(string $field, mixed $value, ?string $table): bool
    {
        if (!$table || !$value) {
            return false;
        }
        
        $sql = "SELECT COUNT(*) FROM {$table} WHERE {$field} = :value";
        $count = $this->db->query($sql, ['value' => $value])->fetchColumn();
        
        return $count > 0;
    }

    /**
     * Handle validation failure
     */
    protected function validationFailed(array $errors): never
    {
        $request = $this->getRequest();
        if ($request->expectsJson()) {
            $response = JsonResponse::validationError($errors);
        } else {
            $this->session->flash('validation_errors', $errors);
            $this->session->flash('old_input', $request->getBody());
            $response = Response::redirect($request->getHeader('Referer', '/'));
        }
        
        $response->send();
        exit;
    }

    /**
     * Get authenticated user
     */
    protected function getUser(): ?array
    {
        return $this->session->get('user');
    }

    /**
     * Check if user is authenticated
     */
    protected function isAuthenticated(): bool
    {
        return $this->getUser() !== null;
    }

    /**
     * Require authentication
     */
    protected function requireAuth(): void
    {
        if (!$this->isAuthenticated()) {
            $request = $this->getRequest();
            if ($request->expectsJson()) {
                JsonResponse::unauthorized()->send();
            } else {
                Response::redirect('/login')->send();
            }
            exit;
        }
    }

    /**
     * Check user role
     */
    protected function hasRole(string $role): bool
    {
        $user = $this->getUser();
        return $user && $user['role'] === $role;
    }

    /**
     * Require specific role
     */
    protected function requireRole(string $role): void
    {
        $this->requireAuth();
        
        if (!$this->hasRole($role)) {
            $request = $this->getRequest();
            if ($request->expectsJson()) {
                JsonResponse::forbidden()->send();
            } else {
                Response::redirect('/')->send();
            }
            exit;
        }
    }

    /**
     * Log activity
     */
    protected function logActivity(string $action, string $description = '', array $properties = []): void
    {
        $user = $this->getUser();
        
        $request = $this->getRequest();
        $this->db->insert('activity_logs', [
            'user_id' => $user['id'] ?? null,
            'action' => $action,
            'description' => $description,
            'properties' => json_encode($properties),
            'ip_address' => $request->getClientIp(),
            'user_agent' => $request->getUserAgent()
        ]);
    }

    /**
     * Return JSON response
     */
    protected function json(array $data = [], int $statusCode = 200): JsonResponse
    {
        return new JsonResponse($data, $statusCode);
    }

    /**
     * Return success JSON response
     */
    protected function success(array $data = [], string $message = 'Success'): JsonResponse
    {
        return JsonResponse::success($data, $message);
    }

    /**
     * Return error JSON response
     */
    protected function error(string $message = 'Error', int $statusCode = 400): JsonResponse
    {
        return JsonResponse::error($message, [], $statusCode);
    }

    /**
     * Return paginated response
     */
    protected function paginated(array $items, int $total, int $page = 1, int $perPage = 15): JsonResponse
    {
        return JsonResponse::paginated($items, $total, $page, $perPage);
    }

    /**
     * Get pagination parameters
     */
    protected function getPaginationParams(): array
    {
        $page = max(1, (int)$this->request->query('page', 1));
        $perPage = min(100, max(1, (int)$this->request->query('per_page', 15)));
        $offset = ($page - 1) * $perPage;
        
        return compact('page', 'perPage', 'offset');
    }

    /**
     * Upload file
     */
    protected function uploadFile(string $fieldName, string $directory = 'uploads'): ?array
    {
        $request = $this->getRequest();
        $file = $request->file($fieldName);
        
        if (!$file || $file['error'] !== UPLOAD_ERR_OK) {
            return null;
        }
        
        $uploadPath = STORAGE_PATH . '/' . $directory;
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0755, true);
        }
        
        $filename = uniqid() . '_' . $file['name'];
        $filepath = $uploadPath . '/' . $filename;
        
        if (move_uploaded_file($file['tmp_name'], $filepath)) {
            return [
                'filename' => $filename,
                'original_name' => $file['name'],
                'size' => $file['size'],
                'type' => $file['type'],
                'path' => $filepath,
                'url' => '/storage/' . $directory . '/' . $filename
            ];
        }
        
        return null;
    }

    /**
     * Generate CSRF token
     */
    protected function generateCsrfToken(): string
    {
        $token = bin2hex(random_bytes(32));
        $this->session->set('csrf_token', $token);
        return $token;
    }

    /**
     * Verify CSRF token
     */
    protected function verifyCsrfToken(string $token): bool
    {
        $sessionToken = $this->session->get('csrf_token');
        return $sessionToken && hash_equals($sessionToken, $token);
    }

    /**
     * Rate limit check
     */
    protected function rateLimit(string $key, int $maxAttempts = 60, int $decayMinutes = 1): bool
    {
        $cacheKey = "rate_limit:{$key}";
        $attempts = $this->cache->get($cacheKey, 0);
        
        if ($attempts >= $maxAttempts) {
            return false;
        }
        
        $this->cache->put($cacheKey, $attempts + 1, $decayMinutes * 60);
        return true;
    }
}
