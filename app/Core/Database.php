<?php

declare(strict_types=1);

namespace App\Core;

use PDO;
use PDOException;
use Exception;

/**
 * Database connection and query management
 */
class Database
{
    private Config $config;
    private ?PDO $connection = null;
    private array $queryLog = [];
    private bool $logQueries = false;

    public function __construct(Config $config)
    {
        $this->config = $config;
        $this->logQueries = $config->get('app.debug', false);
    }

    /**
     * Get database connection
     */
    public function getConnection(): PDO
    {
        if ($this->connection === null) {
            $this->connect();
        }

        return $this->connection;
    }

    /**
     * Establish database connection
     */
    private function connect(): void
    {
        $dbConfig = $this->config->get('database');
        
        $dsn = sprintf(
            '%s:host=%s;port=%d;dbname=%s;charset=%s',
            $dbConfig['connection'],
            $dbConfig['host'],
            $dbConfig['port'],
            $dbConfig['database'],
            $dbConfig['charset']
        );

        try {
            $this->connection = new PDO(
                $dsn,
                $dbConfig['username'],
                $dbConfig['password'],
                $dbConfig['options']
            );
        } catch (PDOException $e) {
            throw new Exception("Database connection failed: " . $e->getMessage());
        }
    }

    /**
     * Execute a query
     */
    public function query(string $sql, array $params = []): \PDOStatement
    {
        $startTime = microtime(true);
        
        try {
            $statement = $this->getConnection()->prepare($sql);
            $statement->execute($params);
            
            if ($this->logQueries) {
                $this->queryLog[] = [
                    'sql' => $sql,
                    'params' => $params,
                    'time' => microtime(true) - $startTime
                ];
            }
            
            return $statement;
        } catch (PDOException $e) {
            throw new Exception("Query failed: " . $e->getMessage() . " SQL: " . $sql);
        }
    }

    /**
     * Fetch all results
     */
    public function fetchAll(string $sql, array $params = []): array
    {
        return $this->query($sql, $params)->fetchAll();
    }

    /**
     * Fetch single result
     */
    public function fetchOne(string $sql, array $params = []): ?array
    {
        $result = $this->query($sql, $params)->fetch();
        return $result ?: null;
    }

    /**
     * Insert record and return ID
     */
    public function insert(string $table, array $data): int
    {
        $columns = array_keys($data);
        $placeholders = array_map(fn($col) => ':' . $col, $columns);
        
        $sql = sprintf(
            'INSERT INTO %s (%s) VALUES (%s)',
            $table,
            implode(', ', $columns),
            implode(', ', $placeholders)
        );
        
        $this->query($sql, $data);
        
        return (int)$this->getConnection()->lastInsertId();
    }

    /**
     * Update records
     */
    public function update(string $table, array $data, array $where): int
    {
        $setParts = array_map(fn($col) => $col . ' = :' . $col, array_keys($data));
        $whereParts = array_map(fn($col) => $col . ' = :where_' . $col, array_keys($where));
        
        $sql = sprintf(
            'UPDATE %s SET %s WHERE %s',
            $table,
            implode(', ', $setParts),
            implode(' AND ', $whereParts)
        );
        
        // Prefix where parameters to avoid conflicts
        $whereParams = [];
        foreach ($where as $key => $value) {
            $whereParams['where_' . $key] = $value;
        }
        
        $params = array_merge($data, $whereParams);
        $statement = $this->query($sql, $params);
        
        return $statement->rowCount();
    }

    /**
     * Delete records
     */
    public function delete(string $table, array $where): int
    {
        $whereParts = array_map(fn($col) => $col . ' = :' . $col, array_keys($where));
        
        $sql = sprintf(
            'DELETE FROM %s WHERE %s',
            $table,
            implode(' AND ', $whereParts)
        );
        
        $statement = $this->query($sql, $where);
        
        return $statement->rowCount();
    }

    /**
     * Begin transaction
     */
    public function beginTransaction(): bool
    {
        return $this->getConnection()->beginTransaction();
    }

    /**
     * Commit transaction
     */
    public function commit(): bool
    {
        return $this->getConnection()->commit();
    }

    /**
     * Rollback transaction
     */
    public function rollback(): bool
    {
        return $this->getConnection()->rollBack();
    }

    /**
     * Execute transaction with callback
     */
    public function transaction(callable $callback): mixed
    {
        $this->beginTransaction();
        
        try {
            $result = $callback($this);
            $this->commit();
            return $result;
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }

    /**
     * Check if table exists
     */
    public function tableExists(string $table): bool
    {
        $sql = "SHOW TABLES LIKE :table";
        $result = $this->fetchOne($sql, ['table' => $table]);
        
        return $result !== null;
    }

    /**
     * Run database migrations
     */
    public function migrate(): void
    {
        // Create migrations table if it doesn't exist
        $this->createMigrationsTable();
        
        $migrationsPath = ROOT_PATH . '/database/migrations';
        
        if (!is_dir($migrationsPath)) {
            return;
        }
        
        $files = glob($migrationsPath . '/*.sql');
        sort($files);
        
        foreach ($files as $file) {
            $filename = basename($file);
            
            if ($this->migrationExists($filename)) {
                continue;
            }
            
            echo "Running migration: {$filename}\n";
            
            $sql = file_get_contents($file);
            $this->getConnection()->exec($sql);
            
            $this->recordMigration($filename);
        }
    }

    /**
     * Create migrations tracking table
     */
    private function createMigrationsTable(): void
    {
        $sql = "
            CREATE TABLE IF NOT EXISTS migrations (
                id INT AUTO_INCREMENT PRIMARY KEY,
                migration VARCHAR(255) NOT NULL,
                executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ";
        
        $this->getConnection()->exec($sql);
    }

    /**
     * Check if migration has been run
     */
    private function migrationExists(string $migration): bool
    {
        $sql = "SELECT COUNT(*) FROM migrations WHERE migration = :migration";
        $result = $this->query($sql, ['migration' => $migration])->fetchColumn();
        
        return $result > 0;
    }

    /**
     * Record migration as executed
     */
    private function recordMigration(string $migration): void
    {
        $this->insert('migrations', ['migration' => $migration]);
    }

    /**
     * Get query log
     */
    public function getQueryLog(): array
    {
        return $this->queryLog;
    }

    /**
     * Clear query log
     */
    public function clearQueryLog(): void
    {
        $this->queryLog = [];
    }

    /**
     * Get total query time
     */
    public function getTotalQueryTime(): float
    {
        return array_sum(array_column($this->queryLog, 'time'));
    }
}
