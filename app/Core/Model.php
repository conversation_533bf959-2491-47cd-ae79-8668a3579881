<?php

declare(strict_types=1);

namespace App\Core;

use Exception;

/**
 * Base Model class
 * Provides basic ORM functionality
 */
abstract class Model
{
    protected Database $db;
    protected string $table = '';
    protected string $primaryKey = 'id';
    protected array $fillable = [];
    protected array $hidden = [];
    protected array $casts = [];
    protected bool $timestamps = true;
    protected array $attributes = [];
    protected array $original = [];
    protected bool $exists = false;

    public function __construct(Database $db, array $attributes = [])
    {
        $this->db = $db;
        
        if (!empty($attributes)) {
            $this->fill($attributes);
            $this->exists = true;
            $this->original = $this->attributes;
        }
    }

    /**
     * Get table name
     */
    public function getTable(): string
    {
        if (empty($this->table)) {
            $className = (new \ReflectionClass($this))->getShortName();
            $this->table = strtolower(preg_replace('/([a-z])([A-Z])/', '$1_$2', $className)) . 's';
        }
        
        return $this->table;
    }

    /**
     * Fill model with attributes
     */
    public function fill(array $attributes): self
    {
        foreach ($attributes as $key => $value) {
            if (empty($this->fillable) || in_array($key, $this->fillable)) {
                $this->setAttribute($key, $value);
            }
        }
        
        return $this;
    }

    /**
     * Set attribute value
     */
    public function setAttribute(string $key, mixed $value): void
    {
        $this->attributes[$key] = $this->castAttribute($key, $value);
    }

    /**
     * Get attribute value
     */
    public function getAttribute(string $key): mixed
    {
        if (!array_key_exists($key, $this->attributes)) {
            return null;
        }
        
        return $this->castAttribute($key, $this->attributes[$key]);
    }

    /**
     * Cast attribute to appropriate type
     */
    protected function castAttribute(string $key, mixed $value): mixed
    {
        if (!isset($this->casts[$key])) {
            return $value;
        }
        
        $cast = $this->casts[$key];
        
        return match ($cast) {
            'int', 'integer' => (int)$value,
            'float', 'double' => (float)$value,
            'bool', 'boolean' => (bool)$value,
            'string' => (string)$value,
            'array', 'json' => is_string($value) ? json_decode($value, true) : $value,
            'datetime' => $value instanceof \DateTime ? $value : new \DateTime($value),
            default => $value
        };
    }

    /**
     * Magic getter
     */
    public function __get(string $key): mixed
    {
        return $this->getAttribute($key);
    }

    /**
     * Magic setter
     */
    public function __set(string $key, mixed $value): void
    {
        $this->setAttribute($key, $value);
    }

    /**
     * Check if attribute exists
     */
    public function __isset(string $key): bool
    {
        return array_key_exists($key, $this->attributes);
    }

    /**
     * Save model to database
     */
    public function save(): bool
    {
        if ($this->timestamps) {
            $now = date('Y-m-d H:i:s');
            
            if (!$this->exists) {
                $this->setAttribute('created_at', $now);
            }
            
            $this->setAttribute('updated_at', $now);
        }
        
        if ($this->exists) {
            return $this->performUpdate();
        } else {
            return $this->performInsert();
        }
    }

    /**
     * Perform insert operation
     */
    protected function performInsert(): bool
    {
        $attributes = $this->getAttributesForInsert();
        
        if (empty($attributes)) {
            return false;
        }
        
        $id = $this->db->insert($this->getTable(), $attributes);
        
        if ($id) {
            $this->setAttribute($this->primaryKey, $id);
            $this->exists = true;
            $this->original = $this->attributes;
            return true;
        }
        
        return false;
    }

    /**
     * Perform update operation
     */
    protected function performUpdate(): bool
    {
        $dirty = $this->getDirtyAttributes();
        
        if (empty($dirty)) {
            return true; // No changes to save
        }
        
        $where = [$this->primaryKey => $this->getAttribute($this->primaryKey)];
        $affected = $this->db->update($this->getTable(), $dirty, $where);
        
        if ($affected > 0) {
            $this->original = $this->attributes;
            return true;
        }
        
        return false;
    }

    /**
     * Delete model from database
     */
    public function delete(): bool
    {
        if (!$this->exists) {
            return false;
        }
        
        $where = [$this->primaryKey => $this->getAttribute($this->primaryKey)];
        $affected = $this->db->delete($this->getTable(), $where);
        
        if ($affected > 0) {
            $this->exists = false;
            return true;
        }
        
        return false;
    }

    /**
     * Get attributes for insert
     */
    protected function getAttributesForInsert(): array
    {
        $attributes = $this->attributes;
        
        // Remove primary key if it's auto-increment
        if (isset($attributes[$this->primaryKey]) && empty($attributes[$this->primaryKey])) {
            unset($attributes[$this->primaryKey]);
        }
        
        return $this->prepareAttributesForDatabase($attributes);
    }

    /**
     * Get dirty (changed) attributes
     */
    protected function getDirtyAttributes(): array
    {
        $dirty = [];
        
        foreach ($this->attributes as $key => $value) {
            if (!array_key_exists($key, $this->original) || $this->original[$key] !== $value) {
                $dirty[$key] = $value;
            }
        }
        
        return $this->prepareAttributesForDatabase($dirty);
    }

    /**
     * Prepare attributes for database storage
     */
    protected function prepareAttributesForDatabase(array $attributes): array
    {
        foreach ($attributes as $key => $value) {
            if (isset($this->casts[$key])) {
                $cast = $this->casts[$key];
                
                if (in_array($cast, ['array', 'json']) && is_array($value)) {
                    $attributes[$key] = json_encode($value);
                } elseif ($cast === 'datetime' && $value instanceof \DateTime) {
                    $attributes[$key] = $value->format('Y-m-d H:i:s');
                }
            }
        }
        
        return $attributes;
    }

    /**
     * Find model by ID
     */
    public static function find(Database $db, mixed $id): ?static
    {
        $instance = new static($db);
        
        $sql = "SELECT * FROM {$instance->getTable()} WHERE {$instance->primaryKey} = :id LIMIT 1";
        $result = $db->fetchOne($sql, ['id' => $id]);
        
        if ($result) {
            return new static($db, $result);
        }
        
        return null;
    }

    /**
     * Find model by criteria
     */
    public static function where(Database $db, array $criteria): array
    {
        $instance = new static($db);
        
        $whereParts = array_map(fn($key) => "{$key} = :{$key}", array_keys($criteria));
        $sql = "SELECT * FROM {$instance->getTable()} WHERE " . implode(' AND ', $whereParts);
        
        $results = $db->fetchAll($sql, $criteria);
        
        return array_map(fn($row) => new static($db, $row), $results);
    }

    /**
     * Get all models
     */
    public static function all(Database $db): array
    {
        $instance = new static($db);
        
        $sql = "SELECT * FROM {$instance->getTable()}";
        $results = $db->fetchAll($sql);
        
        return array_map(fn($row) => new static($db, $row), $results);
    }

    /**
     * Create new model
     */
    public static function create(Database $db, array $attributes): static
    {
        $instance = new static($db);
        $instance->fill($attributes);
        $instance->save();
        
        return $instance;
    }

    /**
     * Convert model to array
     */
    public function toArray(): array
    {
        $attributes = $this->attributes;
        
        // Remove hidden attributes
        foreach ($this->hidden as $hidden) {
            unset($attributes[$hidden]);
        }
        
        return $attributes;
    }

    /**
     * Convert model to JSON
     */
    public function toJson(): string
    {
        return json_encode($this->toArray());
    }
}
