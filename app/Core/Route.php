<?php

declare(strict_types=1);

namespace App\Core;

/**
 * Route class
 * Represents a single route definition
 */
class Route
{
    private array $methods;
    private string $uri;
    private $action;
    private array $middleware = [];
    private ?string $name = null;
    private array $parameters = [];
    private string $regex = '';
    private array $parameterNames = [];

    public function __construct(array $methods, string $uri, $action)
    {
        $this->methods = array_map('strtoupper', $methods);
        $this->uri = $uri;
        $this->action = $action;
        $this->compileRoute();
    }

    /**
     * Add middleware to the route
     */
    public function middleware(string|array $middleware): self
    {
        if (is_string($middleware)) {
            $middleware = [$middleware];
        }
        
        $this->middleware = array_merge($this->middleware, $middleware);
        return $this;
    }

    /**
     * Set route name
     */
    public function name(string $name): self
    {
        $this->name = $name;
        return $this;
    }

    /**
     * Add prefix to route URI
     */
    public function prefix(string $prefix): self
    {
        $this->uri = '/' . trim($prefix, '/') . '/' . ltrim($this->uri, '/');
        $this->compileRoute();
        return $this;
    }

    /**
     * Set namespace for controller action
     */
    public function namespace(string $namespace): self
    {
        if (is_string($this->action)) {
            $this->action = $namespace . '\\' . $this->action;
        } elseif (is_array($this->action)) {
            $this->action[0] = $namespace . '\\' . $this->action[0];
        }
        
        return $this;
    }

    /**
     * Check if route matches the given method and URI
     */
    public function matches(string $method, string $uri): bool
    {
        if (!in_array($method, $this->methods)) {
            return false;
        }

        return preg_match($this->regex, $uri) === 1;
    }

    /**
     * Get route parameters from URI
     */
    public function getParameters(string $uri): array
    {
        if (empty($this->parameterNames)) {
            return [];
        }

        preg_match($this->regex, $uri, $matches);
        array_shift($matches); // Remove full match

        $parameters = [];
        foreach ($this->parameterNames as $index => $name) {
            $parameters[$name] = $matches[$index] ?? null;
        }

        return array_values($parameters);
    }

    /**
     * Compile the route pattern into a regex
     */
    private function compileRoute(): void
    {
        $uri = $this->uri;
        
        // Normalize slashes
        $uri = '/' . trim($uri, '/');
        if ($uri === '/') {
            $this->regex = '#^/$#';
            return;
        }

        // Find parameters
        preg_match_all('/\{([^}]+)\}/', $uri, $matches);
        $this->parameterNames = $matches[1];

        // Replace parameters with regex patterns
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $uri);
        
        // Escape special regex characters
        $pattern = str_replace('/', '\/', $pattern);
        
        $this->regex = '#^' . $pattern . '$#';
    }

    /**
     * Get route methods
     */
    public function getMethods(): array
    {
        return $this->methods;
    }

    /**
     * Get route URI
     */
    public function getUri(): string
    {
        return $this->uri;
    }

    /**
     * Get route action
     */
    public function getAction()
    {
        return $this->action;
    }

    /**
     * Get route middleware
     */
    public function getMiddleware(): array
    {
        return $this->middleware;
    }

    /**
     * Get route name
     */
    public function getName(): ?string
    {
        return $this->name;
    }

    /**
     * Get route regex pattern
     */
    public function getRegex(): string
    {
        return $this->regex;
    }

    /**
     * Get parameter names
     */
    public function getParameterNames(): array
    {
        return $this->parameterNames;
    }
}
