<?php

declare(strict_types=1);

namespace App\Core;

use App\Core\Http\Request;
use App\Core\Http\Response;
use App\Core\Http\JsonResponse;
use App\Core\Container;
use Exception;

/**
 * Router class
 * Handles route registration and dispatching
 */
class Router
{
    private Container $container;
    private array $routes = [];
    private array $groupStack = [];
    private array $namedRoutes = [];

    public function __construct(Container $container)
    {
        $this->container = $container;
    }

    /**
     * Register a GET route
     */
    public function get(string $uri, $action): Route
    {
        return $this->addRoute(['GET'], $uri, $action);
    }

    /**
     * Register a POST route
     */
    public function post(string $uri, $action): Route
    {
        return $this->addRoute(['POST'], $uri, $action);
    }

    /**
     * Register a PUT route
     */
    public function put(string $uri, $action): Route
    {
        return $this->addRoute(['PUT'], $uri, $action);
    }

    /**
     * Register a DELETE route
     */
    public function delete(string $uri, $action): Route
    {
        return $this->addRoute(['DELETE'], $uri, $action);
    }

    /**
     * Register a PATCH route
     */
    public function patch(string $uri, $action): Route
    {
        return $this->addRoute(['PATCH'], $uri, $action);
    }

    /**
     * Register a route for multiple methods
     */
    public function match(array $methods, string $uri, $action): Route
    {
        return $this->addRoute($methods, $uri, $action);
    }

    /**
     * Register a route for all methods
     */
    public function any(string $uri, $action): Route
    {
        return $this->addRoute(['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'], $uri, $action);
    }

    /**
     * Create a route group
     */
    public function group(array $attributes, callable $callback): void
    {
        $this->groupStack[] = $attributes;
        $callback($this);
        array_pop($this->groupStack);
    }

    /**
     * Add a route to the collection
     */
    private function addRoute(array $methods, string $uri, $action): Route
    {
        $route = new Route($methods, $this->prefix($uri), $action);
        
        // Apply group attributes
        if (!empty($this->groupStack)) {
            $route = $this->applyGroupAttributes($route);
        }

        $this->routes[] = $route;
        
        return $route;
    }

    /**
     * Apply group attributes to route
     */
    private function applyGroupAttributes(Route $route): Route
    {
        foreach ($this->groupStack as $group) {
            if (isset($group['middleware'])) {
                $route->middleware($group['middleware']);
            }
            
            if (isset($group['prefix'])) {
                $route->prefix($group['prefix']);
            }
            
            if (isset($group['namespace'])) {
                $route->namespace($group['namespace']);
            }
        }
        
        return $route;
    }

    /**
     * Prefix the URI with group prefixes
     */
    private function prefix(string $uri): string
    {
        $prefix = '';
        
        foreach ($this->groupStack as $group) {
            if (isset($group['prefix'])) {
                $prefix .= '/' . trim($group['prefix'], '/');
            }
        }
        
        return $prefix . '/' . ltrim($uri, '/');
    }

    /**
     * Dispatch the request to the appropriate route
     */
    public function dispatch(Request $request): Response
    {
        $method = $request->getMethod();
        $uri = $request->getPath();

        foreach ($this->routes as $route) {
            if ($route->matches($method, $uri)) {
                return $this->runRoute($request, $route);
            }
        }

        // No route found
        return $this->handleNotFound($request);
    }

    /**
     * Run the matched route
     */
    private function runRoute(Request $request, Route $route): Response
    {
        try {
            // Extract route parameters
            $parameters = $route->getParameters($request->getPath());
            
            // Run route middleware
            $response = $this->runRouteMiddleware($request, $route, function() use ($request, $route, $parameters) {
                return $this->callAction($request, $route, $parameters);
            });

            return $response;
            
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * Run route-specific middleware
     */
    private function runRouteMiddleware(Request $request, Route $route, callable $next): Response
    {
        $middleware = $route->getMiddleware();
        
        if (empty($middleware)) {
            return $next();
        }

        // Build middleware pipeline
        $pipeline = array_reduce(array_reverse($middleware), function ($next, $middlewareClass) use ($request) {
            return function () use ($middlewareClass, $next, $request) {
                $middleware = $this->container->get($middlewareClass);
                return $middleware->handle($request, $next);
            };
        }, $next);

        return $pipeline();
    }

    /**
     * Call the route action
     */
    private function callAction(Request $request, Route $route, array $parameters): Response
    {
        $action = $route->getAction();

        if (is_callable($action)) {
            $result = $action($request, ...$parameters);
        } elseif (is_array($action)) {
            [$controller, $method] = $action;
            $controllerInstance = $this->container->get($controller);
            $result = $this->container->call([$controllerInstance, $method], $parameters);
        } elseif (is_string($action)) {
            // Handle "Controller@method" syntax
            if (strpos($action, '@') !== false) {
                [$controller, $method] = explode('@', $action, 2);
                $controllerInstance = $this->container->get($controller);
                $result = $this->container->call([$controllerInstance, $method], $parameters);
            } else {
                throw new Exception("Invalid route action: {$action}");
            }
        } else {
            throw new Exception("Invalid route action type");
        }

        // Convert result to Response if needed
        if ($result instanceof Response) {
            return $result;
        }

        if (is_array($result)) {
            return new JsonResponse($result);
        }

        return new Response((string)$result);
    }

    /**
     * Handle 404 Not Found
     */
    private function handleNotFound(Request $request): Response
    {
        if ($request->expectsJson()) {
            return JsonResponse::notFound('Route not found');
        }

        return new Response('<h1>404 - Not Found</h1><p>The requested page could not be found.</p>', 404);
    }

    /**
     * Get all registered routes
     */
    public function getRoutes(): array
    {
        return $this->routes;
    }
}
