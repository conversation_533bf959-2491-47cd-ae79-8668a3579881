<?php

declare(strict_types=1);

namespace App\Core;

use App\Core\Http\Response;

/**
 * View class for rendering templates
 */
class View
{
    private string $viewsPath;
    private array $data = [];
    private string $layout = 'layouts/app';

    public function __construct(?string $viewsPath = null)
    {
        $this->viewsPath = $viewsPath ?: ROOT_PATH . '/resources/views';
    }

    /**
     * Set data for the view
     */
    public function with(array $data): self
    {
        $this->data = array_merge($this->data, $data);
        return $this;
    }

    /**
     * Set layout for the view
     */
    public function layout(?string $layout): self
    {
        $this->layout = $layout;
        return $this;
    }

    /**
     * Render a view
     */
    public function render(string $view, array $data = []): string
    {
        $this->data = array_merge($this->data, $data);
        
        // Extract data to variables
        extract($this->data);
        
        // Start output buffering
        ob_start();
        
        // Include the view file
        $viewFile = $this->viewsPath . '/' . $view . '.php';
        if (!file_exists($viewFile)) {
            throw new \Exception("View file not found: {$viewFile}");
        }
        
        include $viewFile;
        
        // Get the content
        $content = ob_get_clean();
        
        // If no layout, return content directly
        if (!$this->layout) {
            return $content;
        }
        
        // Render with layout
        return $this->renderWithLayout($content);
    }

    /**
     * Render content with layout
     */
    private function renderWithLayout(string $content): string
    {
        // Extract data to variables and add content
        $data = array_merge($this->data, ['content' => $content]);
        extract($data);

        // Start output buffering
        ob_start();

        // Include the layout file
        $layoutFile = $this->viewsPath . '/' . $this->layout . '.php';
        if (!file_exists($layoutFile)) {
            throw new \Exception("Layout file not found: {$layoutFile}");
        }

        include $layoutFile;

        return ob_get_clean();
    }

    /**
     * Create a view instance
     */
    public static function make(string $view, array $data = []): self
    {
        $instance = new self();
        $instance->data = $data;
        return $instance;
    }

    /**
     * Render and return as Response
     */
    public function response(string $view, array $data = []): Response
    {
        $content = $this->render($view, $data);
        return new Response($content);
    }

    /**
     * Include a partial view
     */
    public function partial(string $partial, array $data = []): string
    {
        // Merge with current data
        $data = array_merge($this->data, $data);
        
        // Extract data to variables
        extract($data);
        
        // Start output buffering
        ob_start();
        
        // Include the partial file
        $partialFile = $this->viewsPath . '/partials/' . $partial . '.php';
        if (!file_exists($partialFile)) {
            throw new \Exception("Partial file not found: {$partialFile}");
        }
        
        include $partialFile;
        
        return ob_get_clean();
    }

    /**
     * Escape HTML
     */
    public function e(string $value): string
    {
        return htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
    }

    /**
     * Generate URL
     */
    public function url(string $path = ''): string
    {
        $baseUrl = $_ENV['APP_URL'] ?? 'http://localhost:8080';
        return rtrim($baseUrl, '/') . '/' . ltrim($path, '/');
    }

    /**
     * Generate asset URL
     */
    public function asset(string $path): string
    {
        return $this->url('assets/' . ltrim($path, '/'));
    }

    /**
     * Check if user is authenticated
     */
    public function auth(): ?array
    {
        // This would typically get user from session or JWT
        return $_SESSION['user'] ?? null;
    }

    /**
     * Get CSRF token
     */
    public function csrf(): string
    {
        return $_SESSION['csrf_token'] ?? '';
    }

    /**
     * Format date
     */
    public function formatDate(string $date, string $format = 'M j, Y'): string
    {
        return date($format, strtotime($date));
    }

    /**
     * Format currency
     */
    public function formatCurrency(float $amount, string $currency = 'USD'): string
    {
        return number_format($amount, 2) . ' ' . $currency;
    }

    /**
     * Truncate text
     */
    public function truncate(string $text, int $length = 100): string
    {
        return strlen($text) > $length ? substr($text, 0, $length) . '...' : $text;
    }
}
