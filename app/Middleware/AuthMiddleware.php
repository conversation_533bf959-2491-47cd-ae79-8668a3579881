<?php

declare(strict_types=1);

namespace App\Middleware;

use App\Core\Http\Request;
use App\Core\Http\Response;
use App\Core\Http\JsonResponse;
use App\Core\Config;
use App\Core\Database;
use App\Services\JwtService;
use Exception;

/**
 * Authentication Middleware
 * Handles JWT token validation and user authentication
 */
class AuthMiddleware implements MiddlewareInterface
{
    private Config $config;
    private Database $db;
    private JwtService $jwtService;

    public function __construct(Config $config, Database $db, JwtService $jwtService)
    {
        $this->config = $config;
        $this->db = $db;
        $this->jwtService = $jwtService;
    }

    public function handle(Request $request, callable $next): Response
    {
        $token = $this->extractToken($request);
        
        if (!$token) {
            return $this->unauthorized($request, 'Authentication token not provided');
        }

        try {
            $payload = $this->jwtService->decode($token);
            $user = $this->getUserFromPayload($payload);
            
            if (!$user) {
                return $this->unauthorized($request, 'Invalid authentication token');
            }
            
            // Check if user is active
            if ($user['status'] !== 'active') {
                return $this->unauthorized($request, 'Account is not active');
            }
            
            // Add user to request context
            $request->user = $user;
            
            return $next();
            
        } catch (Exception $e) {
            return $this->unauthorized($request, 'Invalid authentication token');
        }
    }

    /**
     * Extract JWT token from request
     */
    private function extractToken(Request $request): ?string
    {
        // Check Authorization header
        $authHeader = $request->getHeader('Authorization');
        if ($authHeader && strpos($authHeader, 'Bearer ') === 0) {
            return substr($authHeader, 7);
        }
        
        // Check query parameter
        $token = $request->query('token');
        if ($token) {
            return $token;
        }
        
        // Check cookie
        $token = $request->cookie('auth_token');
        if ($token) {
            return $token;
        }
        
        return null;
    }

    /**
     * Get user from JWT payload
     */
    private function getUserFromPayload(array $payload): ?array
    {
        if (!isset($payload['user_id'])) {
            return null;
        }
        
        $sql = "SELECT * FROM users WHERE id = :id AND deleted_at IS NULL LIMIT 1";
        return $this->db->fetchOne($sql, ['id' => $payload['user_id']]);
    }

    /**
     * Return unauthorized response
     */
    private function unauthorized(Request $request, string $message): Response
    {
        if ($request->expectsJson()) {
            return JsonResponse::unauthorized($message);
        }
        
        return Response::redirect('/login');
    }
}

/**
 * Optional Authentication Middleware
 * Attempts to authenticate but doesn't require it
 */
class OptionalAuthMiddleware implements MiddlewareInterface
{
    private AuthMiddleware $authMiddleware;

    public function __construct(AuthMiddleware $authMiddleware)
    {
        $this->authMiddleware = $authMiddleware;
    }

    public function handle(Request $request, callable $next): Response
    {
        try {
            // Try to authenticate, but don't fail if it doesn't work
            $this->authMiddleware->handle($request, function() {
                return new Response(); // Dummy response
            });
        } catch (Exception $e) {
            // Authentication failed, but that's okay for optional auth
        }
        
        return $next();
    }
}

/**
 * Role-based Authorization Middleware
 */
class RoleMiddleware implements MiddlewareInterface
{
    private string $requiredRole;

    public function __construct(string $requiredRole)
    {
        $this->requiredRole = $requiredRole;
    }

    public function handle(Request $request, callable $next): Response
    {
        if (!isset($request->user)) {
            return $this->forbidden($request, 'Authentication required');
        }
        
        if ($request->user['role'] !== $this->requiredRole) {
            return $this->forbidden($request, 'Insufficient permissions');
        }
        
        return $next();
    }

    /**
     * Return forbidden response
     */
    private function forbidden(Request $request, string $message): Response
    {
        if ($request->expectsJson()) {
            return JsonResponse::forbidden($message);
        }
        
        return Response::redirect('/');
    }
}

/**
 * Filmmaker Verification Middleware
 * Ensures filmmaker is verified to access certain features
 */
class FilmmakerVerificationMiddleware implements MiddlewareInterface
{
    private Database $db;

    public function __construct(Database $db)
    {
        $this->db = $db;
    }

    public function handle(Request $request, callable $next): Response
    {
        if (!isset($request->user) || $request->user['role'] !== 'filmmaker') {
            return $this->forbidden($request, 'Filmmaker access required');
        }
        
        // Check if filmmaker profile is verified
        $sql = "SELECT verification_status FROM filmmaker_profiles WHERE user_id = :user_id LIMIT 1";
        $profile = $this->db->fetchOne($sql, ['user_id' => $request->user['id']]);
        
        if (!$profile || $profile['verification_status'] !== 'verified') {
            return $this->forbidden($request, 'Filmmaker verification required');
        }
        
        return $next();
    }

    /**
     * Return forbidden response
     */
    private function forbidden(Request $request, string $message): Response
    {
        if ($request->expectsJson()) {
            return JsonResponse::forbidden($message);
        }
        
        return Response::redirect('/dashboard/verification');
    }
}
