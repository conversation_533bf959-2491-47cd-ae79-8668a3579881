<?php

declare(strict_types=1);

namespace App\Middleware;

use App\Core\Http\Request;
use App\Core\Http\Response;
use App\Core\Http\JsonResponse;
use App\Core\Database;
use App\Services\TwoFactorService;

/**
 * Two-Factor Authentication Middleware
 * Ensures 2FA verification for users who have it enabled
 */
class TwoFactorMiddleware implements MiddlewareInterface
{
    private Database $db;
    private TwoFactorService $twoFactorService;

    public function __construct(Database $db, TwoFactorService $twoFactorService)
    {
        $this->db = $db;
        $this->twoFactorService = $twoFactorService;
    }

    public function handle(Request $request, callable $next): Response
    {
        // Skip if user is not authenticated
        if (!isset($request->user)) {
            return $next();
        }
        
        $user = $request->user;
        
        // Skip if 2FA is not enabled for this user
        if (!$user['two_factor_enabled']) {
            return $next();
        }
        
        // Check if 2FA has been verified for this session
        $sessionKey = "2fa_verified_{$user['id']}";
        if ($request->session && $request->session->get($sessionKey)) {
            return $next();
        }
        
        // Check for 2FA bypass routes (like 2FA verification endpoint itself)
        $bypassRoutes = [
            '/api/auth/2fa/verify',
            '/api/auth/2fa/disable',
            '/api/auth/logout',
            '/auth/2fa/verify',
            '/logout'
        ];
        
        if (in_array($request->getPath(), $bypassRoutes)) {
            return $next();
        }
        
        // Require 2FA verification
        return $this->requireTwoFactorVerification($request);
    }

    /**
     * Require 2FA verification
     */
    private function requireTwoFactorVerification(Request $request): Response
    {
        if ($request->expectsJson()) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Two-factor authentication required',
                'requires_2fa' => true
            ], 403);
        }
        
        return Response::redirect('/auth/2fa/verify');
    }
}

/**
 * CSRF Protection Middleware
 */
class CsrfMiddleware implements MiddlewareInterface
{
    public function handle(Request $request, callable $next): Response
    {
        // Skip CSRF for GET, HEAD, OPTIONS requests
        if (in_array($request->getMethod(), ['GET', 'HEAD', 'OPTIONS'])) {
            return $next();
        }
        
        // Skip CSRF for API routes (they use JWT)
        if (strpos($request->getPath(), '/api/') === 0) {
            return $next();
        }
        
        $token = $request->input('_token') ?: $request->getHeader('X-CSRF-Token');
        $sessionToken = $request->session ? $request->session->get('csrf_token') : null;
        
        if (!$token || !$sessionToken || !hash_equals($sessionToken, $token)) {
            if ($request->expectsJson()) {
                return JsonResponse::error('CSRF token mismatch', 419);
            }
            
            return new Response('CSRF token mismatch', 419);
        }
        
        return $next();
    }
}

/**
 * Content Access Middleware
 * Checks if user has access to specific content
 */
class ContentAccessMiddleware implements MiddlewareInterface
{
    private Database $db;

    public function __construct(Database $db)
    {
        $this->db = $db;
    }

    public function handle(Request $request, callable $next): Response
    {
        // Extract content ID from route parameters
        $contentId = $this->extractContentId($request);
        
        if (!$contentId) {
            return $next(); // No content ID to check
        }
        
        // Check if user has access to this content
        if (!$this->hasContentAccess($request, $contentId)) {
            return $this->accessDenied($request);
        }
        
        return $next();
    }

    /**
     * Extract content ID from request
     */
    private function extractContentId(Request $request): ?int
    {
        $path = $request->getPath();
        
        // Match patterns like /api/content/{id} or /watch/{id}
        if (preg_match('/\/(?:api\/)?(?:content|watch)\/(\d+)/', $path, $matches)) {
            return (int)$matches[1];
        }
        
        return null;
    }

    /**
     * Check if user has access to content
     */
    private function hasContentAccess(Request $request, int $contentId): bool
    {
        // Get content details
        $sql = "SELECT c.*, p.filmmaker_id, c.monetization_type 
                FROM content c 
                JOIN filmmaker_platforms p ON c.platform_id = p.id 
                WHERE c.id = :content_id AND c.status = 'published'";
        
        $content = $this->db->fetchOne($sql, ['content_id' => $contentId]);
        
        if (!$content) {
            return false;
        }
        
        // Free content is accessible to everyone
        if ($content['monetization_type'] === 'free') {
            return true;
        }
        
        // Check if user is authenticated
        if (!isset($request->user)) {
            return false;
        }
        
        $userId = $request->user['id'];
        
        // Content owner always has access
        if ($content['filmmaker_id'] == $userId) {
            return true;
        }
        
        // Check user's access rights
        $sql = "SELECT COUNT(*) FROM user_content_access 
                WHERE user_id = :user_id AND content_id = :content_id 
                AND (expires_at IS NULL OR expires_at > NOW())";
        
        $hasAccess = $this->db->query($sql, [
            'user_id' => $userId,
            'content_id' => $contentId
        ])->fetchColumn();
        
        return $hasAccess > 0;
    }

    /**
     * Return access denied response
     */
    private function accessDenied(Request $request): Response
    {
        if ($request->expectsJson()) {
            return JsonResponse::forbidden('Access denied to this content');
        }
        
        return Response::redirect('/pricing');
    }
}

/**
 * Platform Access Middleware
 * Checks if user has access to filmmaker platform features
 */
class PlatformAccessMiddleware implements MiddlewareInterface
{
    private Database $db;

    public function __construct(Database $db)
    {
        $this->db = $db;
    }

    public function handle(Request $request, callable $next): Response
    {
        if (!isset($request->user)) {
            return $this->accessDenied($request, 'Authentication required');
        }
        
        $platformId = $this->extractPlatformId($request);
        
        if (!$platformId) {
            return $next(); // No platform ID to check
        }
        
        // Check if user owns this platform
        $sql = "SELECT filmmaker_id FROM filmmaker_platforms WHERE id = :platform_id";
        $platform = $this->db->fetchOne($sql, ['platform_id' => $platformId]);
        
        if (!$platform || $platform['filmmaker_id'] != $request->user['id']) {
            return $this->accessDenied($request, 'Platform access denied');
        }
        
        return $next();
    }

    /**
     * Extract platform ID from request
     */
    private function extractPlatformId(Request $request): ?int
    {
        $path = $request->getPath();
        
        // Match patterns like /api/platforms/{id}
        if (preg_match('/\/api\/platforms\/(\d+)/', $path, $matches)) {
            return (int)$matches[1];
        }
        
        return null;
    }

    /**
     * Return access denied response
     */
    private function accessDenied(Request $request, string $message): Response
    {
        if ($request->expectsJson()) {
            return JsonResponse::forbidden($message);
        }
        
        return Response::redirect('/dashboard');
    }
}
