<?php

declare(strict_types=1);

namespace App\Models;

use App\Core\Model;
use App\Core\Database;

/**
 * Filmmaker Platform Model
 * Represents a filmmaker's custom OTT platform
 */
class FilmmakerPlatform extends Model
{
    protected string $table = 'filmmaker_platforms';
    
    protected array $fillable = [
        'uuid',
        'filmmaker_id',
        'name',
        'slug',
        'description',
        'tagline',
        'logo_url',
        'banner_url',
        'custom_domain',
        'theme_settings',
        'branding_settings',
        'status',
        'subscription_enabled',
        'ppv_enabled',
        'free_content_enabled',
        'featured_on_discovery',
        'analytics_enabled',
        'custom_analytics_code',
        'seo_title',
        'seo_description',
        'seo_keywords',
        'social_sharing_enabled',
        'comments_enabled',
        'ratings_enabled'
    ];
    
    protected array $hidden = [
        'custom_analytics_code'
    ];
    
    protected array $casts = [
        'theme_settings' => 'json',
        'branding_settings' => 'json',
        'subscription_enabled' => 'boolean',
        'ppv_enabled' => 'boolean',
        'free_content_enabled' => 'boolean',
        'featured_on_discovery' => 'boolean',
        'analytics_enabled' => 'boolean',
        'social_sharing_enabled' => 'boolean',
        'comments_enabled' => 'boolean',
        'ratings_enabled' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * Create a new platform
     */
    public static function createPlatform(Database $db, array $data): self
    {
        // Generate UUID and slug
        $data['uuid'] = uuid();
        $data['slug'] = self::generateUniqueSlug($db, $data['name']);
        
        // Set default values
        $data['status'] = $data['status'] ?? 'draft';
        $data['subscription_enabled'] = $data['subscription_enabled'] ?? true;
        $data['ppv_enabled'] = $data['ppv_enabled'] ?? true;
        $data['free_content_enabled'] = $data['free_content_enabled'] ?? true;
        $data['featured_on_discovery'] = $data['featured_on_discovery'] ?? true;
        $data['analytics_enabled'] = $data['analytics_enabled'] ?? true;
        $data['social_sharing_enabled'] = $data['social_sharing_enabled'] ?? true;
        $data['comments_enabled'] = $data['comments_enabled'] ?? true;
        $data['ratings_enabled'] = $data['ratings_enabled'] ?? true;
        
        return self::create($db, $data);
    }

    /**
     * Generate unique slug
     */
    public static function generateUniqueSlug(Database $db, string $name): string
    {
        $baseSlug = slug($name);
        $slug = $baseSlug;
        $counter = 1;
        
        while (self::slugExists($db, $slug)) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }

    /**
     * Check if slug exists
     */
    public static function slugExists(Database $db, string $slug): bool
    {
        $sql = "SELECT COUNT(*) FROM filmmaker_platforms WHERE slug = :slug";
        $count = $db->query($sql, ['slug' => $slug])->fetchColumn();
        return $count > 0;
    }

    /**
     * Find platform by slug
     */
    public static function findBySlug(Database $db, string $slug): ?self
    {
        $sql = "SELECT * FROM filmmaker_platforms WHERE slug = :slug LIMIT 1";
        $result = $db->fetchOne($sql, ['slug' => $slug]);
        
        return $result ? new self($db, $result) : null;
    }

    /**
     * Find platform by UUID
     */
    public static function findByUuid(Database $db, string $uuid): ?self
    {
        $sql = "SELECT * FROM filmmaker_platforms WHERE uuid = :uuid LIMIT 1";
        $result = $db->fetchOne($sql, ['uuid' => $uuid]);
        
        return $result ? new self($db, $result) : null;
    }

    /**
     * Get platform owner
     */
    public function getOwner(): ?array
    {
        $sql = "SELECT * FROM users WHERE id = :id LIMIT 1";
        return $this->db->fetchOne($sql, ['id' => $this->filmmaker_id]);
    }

    /**
     * Get platform content
     */
    public function getContent(array $filters = []): array
    {
        $sql = "SELECT * FROM content WHERE platform_id = :platform_id";
        $params = ['platform_id' => $this->id];
        
        if (isset($filters['status'])) {
            $sql .= " AND status = :status";
            $params['status'] = $filters['status'];
        }
        
        if (isset($filters['content_type'])) {
            $sql .= " AND content_type = :content_type";
            $params['content_type'] = $filters['content_type'];
        }
        
        $sql .= " ORDER BY created_at DESC";
        
        if (isset($filters['limit'])) {
            $sql .= " LIMIT :limit";
            $params['limit'] = $filters['limit'];
        }
        
        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get platform subscribers count
     */
    public function getSubscribersCount(): int
    {
        $sql = "SELECT COUNT(*) FROM user_subscriptions 
                WHERE platform_id = :platform_id AND status = 'active'";
        
        return (int)$this->db->query($sql, ['platform_id' => $this->id])->fetchColumn();
    }

    /**
     * Get platform revenue
     */
    public function getRevenue(string $period = '30d'): array
    {
        $dateCondition = match($period) {
            '7d' => 'DATE_SUB(NOW(), INTERVAL 7 DAY)',
            '30d' => 'DATE_SUB(NOW(), INTERVAL 30 DAY)',
            '90d' => 'DATE_SUB(NOW(), INTERVAL 90 DAY)',
            '1y' => 'DATE_SUB(NOW(), INTERVAL 1 YEAR)',
            default => 'DATE_SUB(NOW(), INTERVAL 30 DAY)'
        };
        
        $sql = "SELECT 
                    SUM(gross_amount) as total_revenue,
                    SUM(filmmaker_amount) as filmmaker_revenue,
                    SUM(platform_commission) as platform_commission,
                    COUNT(*) as transaction_count
                FROM platform_revenue 
                WHERE platform_id = :platform_id 
                AND created_at >= {$dateCondition}";
        
        return $this->db->fetchOne($sql, ['platform_id' => $this->id]) ?: [
            'total_revenue' => 0,
            'filmmaker_revenue' => 0,
            'platform_commission' => 0,
            'transaction_count' => 0
        ];
    }

    /**
     * Get platform analytics
     */
    public function getAnalytics(string $period = '30d'): array
    {
        $dateCondition = match($period) {
            '7d' => 'DATE_SUB(NOW(), INTERVAL 7 DAY)',
            '30d' => 'DATE_SUB(NOW(), INTERVAL 30 DAY)',
            '90d' => 'DATE_SUB(NOW(), INTERVAL 90 DAY)',
            '1y' => 'DATE_SUB(NOW(), INTERVAL 1 YEAR)',
            default => 'DATE_SUB(NOW(), INTERVAL 30 DAY)'
        };
        
        // Get view analytics
        $viewSql = "SELECT COUNT(*) as total_views
                    FROM analytics_events 
                    WHERE platform_id = :platform_id 
                    AND event_type = 'view'
                    AND created_at >= {$dateCondition}";
        
        $views = $this->db->fetchOne($viewSql, ['platform_id' => $this->id]);
        
        // Get unique viewers
        $viewerSql = "SELECT COUNT(DISTINCT user_id) as unique_viewers
                      FROM analytics_events 
                      WHERE platform_id = :platform_id 
                      AND event_type = 'view'
                      AND created_at >= {$dateCondition}";
        
        $viewers = $this->db->fetchOne($viewerSql, ['platform_id' => $this->id]);
        
        return [
            'total_views' => (int)($views['total_views'] ?? 0),
            'unique_viewers' => (int)($viewers['unique_viewers'] ?? 0),
            'subscribers' => $this->getSubscribersCount(),
            'revenue' => $this->getRevenue($period)
        ];
    }

    /**
     * Check if platform is active
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if platform allows subscriptions
     */
    public function allowsSubscriptions(): bool
    {
        return $this->subscription_enabled;
    }

    /**
     * Check if platform allows pay-per-view
     */
    public function allowsPPV(): bool
    {
        return $this->ppv_enabled;
    }

    /**
     * Get platform URL
     */
    public function getUrl(): string
    {
        if ($this->custom_domain) {
            return 'https://' . $this->custom_domain;
        }
        
        return url('/platform/' . $this->slug);
    }

    /**
     * Get platform theme settings with defaults
     */
    public function getThemeSettings(): array
    {
        $defaults = [
            'primary_color' => '#3B82F6',
            'secondary_color' => '#8B5CF6',
            'background_color' => '#111827',
            'text_color' => '#F9FAFB',
            'font_family' => 'Inter',
            'layout' => 'modern',
            'header_style' => 'transparent',
            'footer_style' => 'minimal'
        ];
        
        return array_merge($defaults, $this->theme_settings ?: []);
    }

    /**
     * Update platform slug
     */
    public function updateSlug(string $newName): bool
    {
        $newSlug = self::generateUniqueSlug($this->db, $newName);
        $this->slug = $newSlug;
        return $this->save();
    }

    /**
     * Convert to array for API responses
     */
    public function toArray(): array
    {
        $data = parent::toArray();
        
        // Add computed fields
        $data['url'] = $this->getUrl();
        $data['subscribers_count'] = $this->getSubscribersCount();
        $data['theme_settings'] = $this->getThemeSettings();
        $data['is_active'] = $this->isActive();
        
        return $data;
    }
}
