<?php

declare(strict_types=1);

namespace App\Models;

use App\Core\Model;
use App\Core\Database;

/**
 * User Model
 * Represents users in the system (viewers, filmmakers, admins)
 */
class User extends Model
{
    protected string $table = 'users';
    
    protected array $fillable = [
        'uuid',
        'email',
        'password',
        'first_name',
        'last_name',
        'username',
        'avatar_url',
        'phone',
        'date_of_birth',
        'gender',
        'country_code',
        'timezone',
        'language_code',
        'role',
        'status',
        'email_notifications',
        'marketing_notifications'
    ];
    
    protected array $hidden = [
        'password',
        'two_factor_secret',
        'two_factor_recovery_codes'
    ];
    
    protected array $casts = [
        'two_factor_enabled' => 'boolean',
        'email_notifications' => 'boolean',
        'marketing_notifications' => 'boolean',
        'two_factor_recovery_codes' => 'json',
        'email_verified_at' => 'datetime',
        'last_login_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    /**
     * Create a new user
     */
    public static function createUser(Database $db, array $data): self
    {
        // Generate UUID
        $data['uuid'] = uuid();
        
        // Hash password
        if (isset($data['password'])) {
            $data['password'] = hash_password($data['password']);
        }
        
        // Set default values
        $data['status'] = $data['status'] ?? 'pending_verification';
        $data['role'] = $data['role'] ?? 'viewer';
        $data['timezone'] = $data['timezone'] ?? 'UTC';
        $data['language_code'] = $data['language_code'] ?? 'en';
        
        return self::create($db, $data);
    }

    /**
     * Find user by email
     */
    public static function findByEmail(Database $db, string $email): ?self
    {
        $sql = "SELECT * FROM users WHERE email = :email AND deleted_at IS NULL LIMIT 1";
        $result = $db->fetchOne($sql, ['email' => $email]);
        
        return $result ? new self($db, $result) : null;
    }

    /**
     * Find user by username
     */
    public static function findByUsername(Database $db, string $username): ?self
    {
        $sql = "SELECT * FROM users WHERE username = :username AND deleted_at IS NULL LIMIT 1";
        $result = $db->fetchOne($sql, ['username' => $username]);
        
        return $result ? new self($db, $result) : null;
    }

    /**
     * Find user by UUID
     */
    public static function findByUuid(Database $db, string $uuid): ?self
    {
        $sql = "SELECT * FROM users WHERE uuid = :uuid AND deleted_at IS NULL LIMIT 1";
        $result = $db->fetchOne($sql, ['uuid' => $uuid]);
        
        return $result ? new self($db, $result) : null;
    }

    /**
     * Verify password
     */
    public function verifyPassword(string $password): bool
    {
        return verify_password($password, $this->password);
    }

    /**
     * Update password
     */
    public function updatePassword(string $newPassword): bool
    {
        $this->password = hash_password($newPassword);
        return $this->save();
    }

    /**
     * Mark email as verified
     */
    public function markEmailAsVerified(): bool
    {
        $this->email_verified_at = now();
        $this->status = 'active';
        return $this->save();
    }

    /**
     * Check if email is verified
     */
    public function isEmailVerified(): bool
    {
        return $this->email_verified_at !== null;
    }

    /**
     * Update last login
     */
    public function updateLastLogin(string $ipAddress): bool
    {
        $this->last_login_at = now();
        $this->last_login_ip = $ipAddress;
        return $this->save();
    }

    /**
     * Check if user is active
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if user has role
     */
    public function hasRole(string $role): bool
    {
        return $this->role === $role;
    }

    /**
     * Check if user is filmmaker
     */
    public function isFilmmaker(): bool
    {
        return $this->hasRole('filmmaker');
    }

    /**
     * Check if user is admin
     */
    public function isAdmin(): bool
    {
        return $this->hasRole('admin');
    }

    /**
     * Get full name
     */
    public function getFullName(): string
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }

    /**
     * Get display name (full name or username)
     */
    public function getDisplayName(): string
    {
        $fullName = $this->getFullName();
        return !empty($fullName) ? $fullName : ($this->username ?? $this->email);
    }

    /**
     * Get avatar URL with fallback
     */
    public function getAvatarUrl(): string
    {
        if ($this->avatar_url) {
            return $this->avatar_url;
        }
        
        // Generate Gravatar URL as fallback
        $hash = md5(strtolower(trim($this->email)));
        return "https://www.gravatar.com/avatar/{$hash}?d=identicon&s=200";
    }

    /**
     * Get user's filmmaker profile
     */
    public function getFilmmakerProfile(): ?array
    {
        if (!$this->isFilmmaker()) {
            return null;
        }
        
        $sql = "SELECT * FROM filmmaker_profiles WHERE user_id = :user_id LIMIT 1";
        return $this->db->fetchOne($sql, ['user_id' => $this->id]);
    }

    /**
     * Get user's platforms
     */
    public function getPlatforms(): array
    {
        if (!$this->isFilmmaker()) {
            return [];
        }
        
        $sql = "SELECT * FROM filmmaker_platforms WHERE filmmaker_id = :user_id ORDER BY created_at DESC";
        return $this->db->fetchAll($sql, ['user_id' => $this->id]);
    }

    /**
     * Get user's subscriptions
     */
    public function getActiveSubscriptions(): array
    {
        $sql = "SELECT s.*, p.name as platform_name, p.slug as platform_slug 
                FROM user_subscriptions s 
                JOIN filmmaker_platforms p ON s.platform_id = p.id 
                WHERE s.user_id = :user_id AND s.status = 'active' 
                ORDER BY s.current_period_end DESC";
        
        return $this->db->fetchAll($sql, ['user_id' => $this->id]);
    }

    /**
     * Check if user has active subscription to platform
     */
    public function hasActiveSubscription(int $platformId): bool
    {
        $sql = "SELECT COUNT(*) FROM user_subscriptions 
                WHERE user_id = :user_id AND platform_id = :platform_id 
                AND status = 'active' AND current_period_end > NOW()";
        
        $count = $this->db->query($sql, [
            'user_id' => $this->id,
            'platform_id' => $platformId
        ])->fetchColumn();
        
        return $count > 0;
    }

    /**
     * Get user's content access
     */
    public function getContentAccess(int $contentId): ?array
    {
        $sql = "SELECT * FROM user_content_access 
                WHERE user_id = :user_id AND content_id = :content_id 
                AND (expires_at IS NULL OR expires_at > NOW()) 
                ORDER BY granted_at DESC LIMIT 1";
        
        return $this->db->fetchOne($sql, [
            'user_id' => $this->id,
            'content_id' => $contentId
        ]);
    }

    /**
     * Soft delete user
     */
    public function softDelete(): bool
    {
        $this->deleted_at = now();
        $this->status = 'inactive';
        return $this->save();
    }

    /**
     * Convert to array for API responses
     */
    public function toArray(): array
    {
        $data = parent::toArray();
        
        // Add computed fields
        $data['full_name'] = $this->getFullName();
        $data['display_name'] = $this->getDisplayName();
        $data['avatar_url'] = $this->getAvatarUrl();
        $data['is_email_verified'] = $this->isEmailVerified();
        $data['is_active'] = $this->isActive();
        
        return $data;
    }
}
