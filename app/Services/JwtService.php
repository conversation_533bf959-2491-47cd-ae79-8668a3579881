<?php

declare(strict_types=1);

namespace App\Services;

use App\Core\Config;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Exception;

/**
 * JWT Service
 * Handles JWT token creation and validation
 */
class JwtService
{
    private Config $config;
    private string $secret;
    private string $algorithm;
    private int $expiry;
    private int $refreshExpiry;

    public function __construct(Config $config)
    {
        $this->config = $config;
        $this->secret = $config->get('jwt.secret');
        $this->algorithm = $config->get('jwt.algorithm', 'HS256');
        $this->expiry = $config->get('jwt.expiry', 3600);
        $this->refreshExpiry = $config->get('jwt.refresh_expiry', 604800);
        
        if (empty($this->secret)) {
            throw new Exception('JWT secret not configured');
        }
    }

    /**
     * Create JWT token for user
     */
    public function createToken(array $user): array
    {
        $now = time();
        $accessTokenExpiry = $now + $this->expiry;
        $refreshTokenExpiry = $now + $this->refreshExpiry;
        
        $accessPayload = [
            'iss' => $this->config->get('app.url'),
            'aud' => $this->config->get('app.url'),
            'iat' => $now,
            'exp' => $accessTokenExpiry,
            'user_id' => $user['id'],
            'email' => $user['email'],
            'role' => $user['role'],
            'type' => 'access'
        ];
        
        $refreshPayload = [
            'iss' => $this->config->get('app.url'),
            'aud' => $this->config->get('app.url'),
            'iat' => $now,
            'exp' => $refreshTokenExpiry,
            'user_id' => $user['id'],
            'type' => 'refresh'
        ];
        
        return [
            'access_token' => JWT::encode($accessPayload, $this->secret, $this->algorithm),
            'refresh_token' => JWT::encode($refreshPayload, $this->secret, $this->algorithm),
            'token_type' => 'Bearer',
            'expires_in' => $this->expiry,
            'expires_at' => $accessTokenExpiry
        ];
    }

    /**
     * Decode and validate JWT token
     */
    public function decode(string $token): array
    {
        try {
            $decoded = JWT::decode($token, new Key($this->secret, $this->algorithm));
            return (array)$decoded;
        } catch (Exception $e) {
            throw new Exception('Invalid token: ' . $e->getMessage());
        }
    }

    /**
     * Refresh access token using refresh token
     */
    public function refreshToken(string $refreshToken): array
    {
        $payload = $this->decode($refreshToken);
        
        if ($payload['type'] !== 'refresh') {
            throw new Exception('Invalid refresh token');
        }
        
        // Get user data
        $user = [
            'id' => $payload['user_id'],
            'email' => $payload['email'] ?? '',
            'role' => $payload['role'] ?? 'viewer'
        ];
        
        return $this->createToken($user);
    }

    /**
     * Validate token without throwing exceptions
     */
    public function validateToken(string $token): bool
    {
        try {
            $this->decode($token);
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Get token expiry time
     */
    public function getTokenExpiry(string $token): ?int
    {
        try {
            $payload = $this->decode($token);
            return $payload['exp'] ?? null;
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * Check if token is expired
     */
    public function isTokenExpired(string $token): bool
    {
        $expiry = $this->getTokenExpiry($token);
        return $expiry && $expiry < time();
    }

    /**
     * Create password reset token
     */
    public function createPasswordResetToken(string $email): string
    {
        $payload = [
            'iss' => $this->config->get('app.url'),
            'aud' => $this->config->get('app.url'),
            'iat' => time(),
            'exp' => time() + 3600, // 1 hour expiry
            'email' => $email,
            'type' => 'password_reset'
        ];
        
        return JWT::encode($payload, $this->secret, $this->algorithm);
    }

    /**
     * Create email verification token
     */
    public function createEmailVerificationToken(int $userId, string $email): string
    {
        $payload = [
            'iss' => $this->config->get('app.url'),
            'aud' => $this->config->get('app.url'),
            'iat' => time(),
            'exp' => time() + 86400, // 24 hours expiry
            'user_id' => $userId,
            'email' => $email,
            'type' => 'email_verification'
        ];
        
        return JWT::encode($payload, $this->secret, $this->algorithm);
    }

    /**
     * Create secure video streaming token
     */
    public function createStreamingToken(int $userId, int $contentId, int $duration = 3600): string
    {
        $payload = [
            'iss' => $this->config->get('app.url'),
            'aud' => $this->config->get('app.url'),
            'iat' => time(),
            'exp' => time() + $duration,
            'user_id' => $userId,
            'content_id' => $contentId,
            'type' => 'streaming',
            'ip' => $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1'
        ];
        
        return JWT::encode($payload, $this->secret, $this->algorithm);
    }

    /**
     * Validate streaming token
     */
    public function validateStreamingToken(string $token, int $contentId): bool
    {
        try {
            $payload = $this->decode($token);
            
            return $payload['type'] === 'streaming' &&
                   $payload['content_id'] === $contentId &&
                   $payload['ip'] === ($_SERVER['REMOTE_ADDR'] ?? '127.0.0.1');
                   
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Create API token for external integrations
     */
    public function createApiToken(int $userId, array $permissions = [], int $expiry = null): string
    {
        $payload = [
            'iss' => $this->config->get('app.url'),
            'aud' => $this->config->get('app.url'),
            'iat' => time(),
            'exp' => $expiry ?: (time() + 31536000), // 1 year default
            'user_id' => $userId,
            'permissions' => $permissions,
            'type' => 'api'
        ];
        
        return JWT::encode($payload, $this->secret, $this->algorithm);
    }

    /**
     * Get user ID from token
     */
    public function getUserIdFromToken(string $token): ?int
    {
        try {
            $payload = $this->decode($token);
            return $payload['user_id'] ?? null;
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * Get token type
     */
    public function getTokenType(string $token): ?string
    {
        try {
            $payload = $this->decode($token);
            return $payload['type'] ?? null;
        } catch (Exception $e) {
            return null;
        }
    }
}
