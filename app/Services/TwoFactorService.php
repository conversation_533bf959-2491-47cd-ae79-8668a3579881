<?php

declare(strict_types=1);

namespace App\Services;

use App\Core\Config;
use App\Core\Database;
use PragmaRX\Google2FA\Google2FA;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;
use Exception;

/**
 * Two-Factor Authentication Service
 * Handles TOTP-based 2FA using Google Authenticator
 */
class TwoFactorService
{
    private Config $config;
    private Database $db;
    private Google2FA $google2fa;

    public function __construct(Config $config, Database $db)
    {
        $this->config = $config;
        $this->db = $db;
        $this->google2fa = new Google2FA();
    }

    /**
     * Generate secret key for 2FA
     */
    public function generateSecretKey(): string
    {
        return $this->google2fa->generateSecretKey();
    }

    /**
     * Generate QR code for 2FA setup
     */
    public function generateQrCode(string $email, string $secret): string
    {
        $issuer = $this->config->get('two_fa.issuer', 'FilmStream');
        $qrCodeUrl = $this->google2fa->getQRCodeUrl($issuer, $email, $secret);
        
        $qrCode = new QrCode($qrCodeUrl);
        $qrCode->setSize(200);
        $qrCode->setMargin(10);
        
        $writer = new PngWriter();
        $result = $writer->write($qrCode);
        
        return 'data:image/png;base64,' . base64_encode($result->getString());
    }

    /**
     * Verify TOTP code
     */
    public function verifyCode(string $secret, string $code): bool
    {
        return $this->google2fa->verifyKey($secret, $code);
    }

    /**
     * Enable 2FA for user
     */
    public function enableTwoFactor(int $userId, string $secret, string $code): bool
    {
        // Verify the code first
        if (!$this->verifyCode($secret, $code)) {
            return false;
        }
        
        // Generate recovery codes
        $recoveryCodes = $this->generateRecoveryCodes();
        
        // Update user record
        $this->db->update('users', [
            'two_factor_enabled' => true,
            'two_factor_secret' => encrypt($secret),
            'two_factor_recovery_codes' => json_encode(array_map('encrypt', $recoveryCodes))
        ], ['id' => $userId]);
        
        return true;
    }

    /**
     * Disable 2FA for user
     */
    public function disableTwoFactor(int $userId): bool
    {
        return $this->db->update('users', [
            'two_factor_enabled' => false,
            'two_factor_secret' => null,
            'two_factor_recovery_codes' => null
        ], ['id' => $userId]) > 0;
    }

    /**
     * Verify 2FA code for user
     */
    public function verifyUserCode(int $userId, string $code): bool
    {
        $user = $this->db->fetchOne(
            "SELECT two_factor_secret, two_factor_recovery_codes FROM users WHERE id = :id",
            ['id' => $userId]
        );
        
        if (!$user || !$user['two_factor_secret']) {
            return false;
        }
        
        $secret = decrypt($user['two_factor_secret']);
        
        // Try regular TOTP code first
        if ($this->verifyCode($secret, $code)) {
            return true;
        }
        
        // Try recovery codes
        return $this->verifyRecoveryCode($userId, $code, $user['two_factor_recovery_codes']);
    }

    /**
     * Verify recovery code
     */
    private function verifyRecoveryCode(int $userId, string $code, ?string $recoveryCodesJson): bool
    {
        if (!$recoveryCodesJson) {
            return false;
        }
        
        $recoveryCodes = json_decode($recoveryCodesJson, true);
        if (!$recoveryCodes) {
            return false;
        }
        
        // Decrypt and check each recovery code
        foreach ($recoveryCodes as $index => $encryptedCode) {
            $recoveryCode = decrypt($encryptedCode);
            
            if (hash_equals($recoveryCode, $code)) {
                // Remove used recovery code
                unset($recoveryCodes[$index]);
                
                // Update user with remaining codes
                $this->db->update('users', [
                    'two_factor_recovery_codes' => json_encode(array_values($recoveryCodes))
                ], ['id' => $userId]);
                
                return true;
            }
        }
        
        return false;
    }

    /**
     * Generate recovery codes
     */
    public function generateRecoveryCodes(int $count = 8): array
    {
        $codes = [];
        
        for ($i = 0; $i < $count; $i++) {
            $codes[] = strtoupper(substr(str_replace(['+', '/', '='], '', base64_encode(random_bytes(6))), 0, 8));
        }
        
        return $codes;
    }

    /**
     * Regenerate recovery codes for user
     */
    public function regenerateRecoveryCodes(int $userId): array
    {
        $recoveryCodes = $this->generateRecoveryCodes();
        
        $this->db->update('users', [
            'two_factor_recovery_codes' => json_encode(array_map('encrypt', $recoveryCodes))
        ], ['id' => $userId]);
        
        return $recoveryCodes;
    }

    /**
     * Get remaining recovery codes count
     */
    public function getRemainingRecoveryCodesCount(int $userId): int
    {
        $user = $this->db->fetchOne(
            "SELECT two_factor_recovery_codes FROM users WHERE id = :id",
            ['id' => $userId]
        );
        
        if (!$user || !$user['two_factor_recovery_codes']) {
            return 0;
        }
        
        $recoveryCodes = json_decode($user['two_factor_recovery_codes'], true);
        return is_array($recoveryCodes) ? count($recoveryCodes) : 0;
    }

    /**
     * Check if user has 2FA enabled
     */
    public function isEnabledForUser(int $userId): bool
    {
        $user = $this->db->fetchOne(
            "SELECT two_factor_enabled FROM users WHERE id = :id",
            ['id' => $userId]
        );
        
        return $user && $user['two_factor_enabled'];
    }

    /**
     * Get 2FA setup data for user
     */
    public function getSetupData(string $email): array
    {
        $secret = $this->generateSecretKey();
        $qrCode = $this->generateQrCode($email, $secret);
        
        return [
            'secret' => $secret,
            'qr_code' => $qrCode,
            'manual_entry_key' => chunk_split($secret, 4, ' ')
        ];
    }

    /**
     * Validate 2FA setup
     */
    public function validateSetup(string $secret, string $code1, string $code2): bool
    {
        // Verify both codes to ensure the user's app is working correctly
        $valid1 = $this->verifyCode($secret, $code1);
        
        // Wait a bit and verify second code (to test time-based nature)
        sleep(1);
        $valid2 = $this->verifyCode($secret, $code2);
        
        return $valid1 && $valid2;
    }

    /**
     * Get backup codes for user (decrypted)
     */
    public function getBackupCodes(int $userId): array
    {
        $user = $this->db->fetchOne(
            "SELECT two_factor_recovery_codes FROM users WHERE id = :id",
            ['id' => $userId]
        );
        
        if (!$user || !$user['two_factor_recovery_codes']) {
            return [];
        }
        
        $encryptedCodes = json_decode($user['two_factor_recovery_codes'], true);
        if (!$encryptedCodes) {
            return [];
        }
        
        return array_map('decrypt', $encryptedCodes);
    }

    /**
     * Check if 2FA is required for sensitive operations
     */
    public function isRequiredForOperation(string $operation, array $user): bool
    {
        if (!$user['two_factor_enabled']) {
            return false;
        }
        
        $sensitiveOperations = [
            'change_password',
            'disable_2fa',
            'delete_account',
            'update_payment_method',
            'withdraw_funds'
        ];
        
        return in_array($operation, $sensitiveOperations);
    }
}
