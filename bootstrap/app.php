<?php

declare(strict_types=1);

use App\Core\Application;
use App\Core\Container;
use App\Core\Router;
use App\Core\Database;
use App\Core\Config;
use App\Core\Logger;
use App\Core\Session;
use App\Core\Cache;
use App\Core\Http\Request;
use App\Middleware\CorsMiddleware;
use App\Middleware\SecurityHeadersMiddleware;
use App\Middleware\RateLimitMiddleware;

/**
 * Bootstrap the application
 */

// Create the dependency injection container
$container = new Container();

// Register core services
$container->singleton(Config::class, function () {
    return new Config();
});

$container->singleton(Database::class, function ($container) {
    $config = $container->get(Config::class);
    return new Database($config);
});

$container->singleton(Logger::class, function ($container) {
    $config = $container->get(Config::class);
    return new Logger($config);
});

$container->singleton(Session::class, function ($container) {
    $config = $container->get(Config::class);
    return new Session($config);
});

$container->singleton(Cache::class, function ($container) {
    $config = $container->get(Config::class);
    return new Cache($config);
});

$container->singleton(Router::class, function ($container) {
    return new Router($container);
});

// Register services
$container->singleton(\App\Services\JwtService::class, function ($container) {
    $config = $container->get(Config::class);
    return new \App\Services\JwtService($config);
});

$container->singleton(\App\Services\TwoFactorService::class, function ($container) {
    $config = $container->get(Config::class);
    $database = $container->get(Database::class);
    return new \App\Services\TwoFactorService($config, $database);
});

// Register Request as singleton to be shared across the application
$container->singleton(Request::class, function () {
    return Request::createFromGlobals();
});

// Create the application instance
$app = new Application($container);

// Register global middleware (order matters)
$app->addMiddleware(CorsMiddleware::class);
$app->addMiddleware(SecurityHeadersMiddleware::class);
$app->addMiddleware(RateLimitMiddleware::class);

// Load routes
require_once ROOT_PATH . '/routes/web.php';
require_once ROOT_PATH . '/routes/api.php';

return $app;
