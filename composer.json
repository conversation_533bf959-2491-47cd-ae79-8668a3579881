{"name": "filmstream/ott-platform", "description": "A filmmaker-centric OTT platform built by filmmakers for filmmakers", "type": "project", "license": "MIT", "authors": [{"name": "FilmStream Team", "email": "<EMAIL>"}], "require": {"php": ">=8.1", "ext-pdo": "*", "ext-json": "*", "ext-curl": "*", "ext-openssl": "*", "firebase/php-jwt": "^6.8", "vlucas/phpdotenv": "^5.5", "ramsey/uuid": "^4.7", "guzzlehttp/guzzle": "^7.8", "league/flysystem": "^3.15", "monolog/monolog": "^3.4", "respect/validation": "^2.2", "pragmarx/google2fa": "^8.0", "endroid/qr-code": "^4.8", "stripe/stripe-php": "^10.15"}, "require-dev": {"phpunit/phpunit": "^10.3", "mockery/mockery": "^1.6", "fakerphp/faker": "^1.23"}, "autoload": {"psr-4": {"App\\": "app/", "Config\\": "config/", "Database\\": "database/"}, "files": ["app/Helpers/functions.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "serve": "php -S localhost:8000 -t public", "migrate": "php artisan migrate", "seed": "php artisan seed"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "stable", "prefer-stable": true}