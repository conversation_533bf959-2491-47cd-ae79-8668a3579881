-- Initial database schema migration
-- This creates all the core tables for the Filmmaker Netflix platform

-- Users table (viewers, filmmakers, admins)
CREATE TABLE IF NOT EXISTS users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    email_verified_at TIMESTAMP NULL,
    password VARCHAR(255) NOT NULL,
    first_name VA<PERSON>HA<PERSON>(100) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    username VA<PERSON>HA<PERSON>(50) UNIQUE,
    avatar_url VARCHAR(500),
    phone VARCHAR(20),
    date_of_birth DATE,
    gender ENUM('male', 'female', 'other', 'prefer_not_to_say'),
    country_code CHAR(2),
    timezone VARCHAR(50) DEFAULT 'UTC',
    language_code CHAR(2) DEFAULT 'en',
    role ENUM('viewer', 'filmmaker', 'admin') NOT NULL DEFAULT 'viewer',
    status ENUM('active', 'inactive', 'suspended', 'pending_verification') DEFAULT 'pending_verification',
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret VARCHAR(255),
    two_factor_recovery_codes JSON,
    last_login_at TIMESTAMP NULL,
    last_login_ip VARCHAR(45),
    email_notifications BOOLEAN DEFAULT TRUE,
    marketing_notifications BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    INDEX idx_email (email),
    INDEX idx_username (username),
    INDEX idx_role (role),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Password reset tokens
CREATE TABLE IF NOT EXISTS password_reset_tokens (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    token VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_email (email),
    INDEX idx_token (token),
    INDEX idx_expires_at (expires_at)
);

-- Email verification tokens
CREATE TABLE IF NOT EXISTS email_verification_tokens (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    token VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    verified_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_token (token),
    INDEX idx_user_id (user_id)
);

-- Content categories
CREATE TABLE IF NOT EXISTS content_categories (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    parent_id BIGINT UNSIGNED NULL,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (parent_id) REFERENCES content_categories(id) ON DELETE SET NULL,
    INDEX idx_slug (slug),
    INDEX idx_parent_id (parent_id),
    INDEX idx_sort_order (sort_order)
);

-- System settings
CREATE TABLE IF NOT EXISTS system_settings (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_type ENUM('string', 'integer', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_setting_key (setting_key),
    INDEX idx_is_public (is_public)
);

-- Insert default system settings
INSERT IGNORE INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('platform_commission_rate', '0.15', 'string', 'Platform commission rate (15%)', FALSE),
('default_currency', 'USD', 'string', 'Default currency for the platform', TRUE),
('supported_currencies', '["USD","EUR","GBP","CAD","AUD"]', 'json', 'Supported currencies', TRUE),
('max_video_size', '5368709120', 'integer', 'Maximum video file size in bytes (5GB)', FALSE),
('allowed_video_formats', '["mp4","mov","avi","mkv","webm"]', 'json', 'Allowed video formats', TRUE),
('video_quality_levels', '["360p","480p","720p","1080p","4k"]', 'json', 'Available video quality levels', TRUE),
('default_rental_duration', '48', 'integer', 'Default rental duration in hours', TRUE),
('platform_name', 'Filmmaker Netflix', 'string', 'Platform name', TRUE),
('platform_description', 'The OTT Platform Built by Filmmakers, for Filmmakers', 'string', 'Platform description', TRUE),
('contact_email', '<EMAIL>', 'string', 'Contact email', TRUE),
('terms_of_service_url', '/terms', 'string', 'Terms of service URL', TRUE),
('privacy_policy_url', '/privacy', 'string', 'Privacy policy URL', TRUE);

-- Insert default content categories
INSERT IGNORE INTO content_categories (name, slug, description, sort_order) VALUES
('Action', 'action', 'Action and adventure films', 1),
('Comedy', 'comedy', 'Comedy films and series', 2),
('Drama', 'drama', 'Dramatic content', 3),
('Documentary', 'documentary', 'Documentary films and series', 4),
('Horror', 'horror', 'Horror and thriller content', 5),
('Romance', 'romance', 'Romantic films and series', 6),
('Sci-Fi', 'sci-fi', 'Science fiction content', 7),
('Animation', 'animation', 'Animated content', 8),
('Short Films', 'short-films', 'Short form content', 9),
('Music Videos', 'music-videos', 'Music videos and performances', 10),
('Educational', 'educational', 'Educational and instructional content', 11),
('Experimental', 'experimental', 'Experimental and avant-garde films', 12);
