-- FilmStream OTT Platform Database Schema
-- Comprehensive schema for multi-tenant filmmaker platform

SET FOREIGN_KEY_CHECKS = 0;
DROP DATABASE IF EXISTS filmstream_ott;
CREATE DATABASE filmstream_ott CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE filmstream_ott;

-- Users table (viewers, filmmakers, admins)
CREATE TABLE users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    email_verified_at TIMESTAMP NULL,
    password VARCHAR(255) NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    username VARCHAR(50) UNIQUE,
    avatar_url VARCHAR(500),
    phone VARCHAR(20),
    date_of_birth DATE,
    gender ENUM('male', 'female', 'other', 'prefer_not_to_say'),
    country_code CHAR(2),
    timezone VARCHAR(50) DEFAULT 'UTC',
    language_code CHAR(2) DEFAULT 'en',
    role ENUM('viewer', 'filmmaker', 'admin') NOT NULL DEFAULT 'viewer',
    status ENUM('active', 'inactive', 'suspended', 'pending_verification') DEFAULT 'pending_verification',
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret VARCHAR(255),
    two_factor_recovery_codes JSON,
    last_login_at TIMESTAMP NULL,
    last_login_ip VARCHAR(45),
    email_notifications BOOLEAN DEFAULT TRUE,
    marketing_notifications BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    INDEX idx_email (email),
    INDEX idx_username (username),
    INDEX idx_role (role),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Password reset tokens
CREATE TABLE password_reset_tokens (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    token VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_email (email),
    INDEX idx_token (token),
    INDEX idx_expires_at (expires_at)
);

-- Email verification tokens
CREATE TABLE email_verification_tokens (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    token VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    verified_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_token (token),
    INDEX idx_user_id (user_id)
);

-- Filmmaker profiles (extended info for filmmakers)
CREATE TABLE filmmaker_profiles (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL UNIQUE,
    company_name VARCHAR(255),
    bio TEXT,
    website_url VARCHAR(500),
    social_media_links JSON,
    specialties JSON, -- Array of specialties like ["documentary", "narrative", "animation"]
    years_experience INT UNSIGNED,
    awards_achievements TEXT,
    portfolio_links JSON,
    verification_status ENUM('pending', 'verified', 'rejected') DEFAULT 'pending',
    verification_documents JSON,
    verified_at TIMESTAMP NULL,
    stripe_connect_account_id VARCHAR(255),
    stripe_connect_status ENUM('pending', 'active', 'restricted', 'inactive') DEFAULT 'pending',
    stripe_onboarding_completed BOOLEAN DEFAULT FALSE,
    payout_enabled BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_verification_status (verification_status),
    INDEX idx_stripe_connect_status (stripe_connect_status)
);

-- Filmmaker platforms (each filmmaker's custom OTT platform)
CREATE TABLE filmmaker_platforms (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) NOT NULL UNIQUE,
    filmmaker_id BIGINT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    tagline VARCHAR(500),
    logo_url VARCHAR(500),
    banner_url VARCHAR(500),
    custom_domain VARCHAR(255),
    theme_settings JSON, -- Colors, fonts, layout preferences
    branding_settings JSON, -- Custom CSS, logos, etc.
    status ENUM('draft', 'active', 'suspended', 'archived') DEFAULT 'draft',
    subscription_enabled BOOLEAN DEFAULT TRUE,
    ppv_enabled BOOLEAN DEFAULT TRUE, -- Pay-per-view
    free_content_enabled BOOLEAN DEFAULT TRUE,
    featured_on_discovery BOOLEAN DEFAULT TRUE,
    analytics_enabled BOOLEAN DEFAULT TRUE,
    custom_analytics_code TEXT,
    seo_title VARCHAR(255),
    seo_description TEXT,
    seo_keywords VARCHAR(500),
    social_sharing_enabled BOOLEAN DEFAULT TRUE,
    comments_enabled BOOLEAN DEFAULT TRUE,
    ratings_enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (filmmaker_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_filmmaker_id (filmmaker_id),
    INDEX idx_slug (slug),
    INDEX idx_status (status),
    INDEX idx_featured_on_discovery (featured_on_discovery)
);

-- Content categories
CREATE TABLE content_categories (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    parent_id BIGINT UNSIGNED NULL,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (parent_id) REFERENCES content_categories(id) ON DELETE SET NULL,
    INDEX idx_slug (slug),
    INDEX idx_parent_id (parent_id),
    INDEX idx_sort_order (sort_order)
);

-- Content (videos, series, etc.)
CREATE TABLE content (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) NOT NULL UNIQUE,
    platform_id BIGINT UNSIGNED NOT NULL,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    description TEXT,
    short_description VARCHAR(500),
    content_type ENUM('movie', 'series', 'episode', 'short', 'documentary', 'trailer') NOT NULL,
    parent_content_id BIGINT UNSIGNED NULL, -- For episodes linking to series
    season_number INT UNSIGNED NULL,
    episode_number INT UNSIGNED NULL,
    duration_seconds INT UNSIGNED,
    release_date DATE,
    content_rating ENUM('G', 'PG', 'PG-13', 'R', 'NC-17', 'NR') DEFAULT 'NR',
    language_code CHAR(2) DEFAULT 'en',
    subtitles_available JSON, -- Array of language codes
    poster_url VARCHAR(500),
    thumbnail_url VARCHAR(500),
    trailer_url VARCHAR(500),
    bunny_video_id VARCHAR(255),
    bunny_library_id VARCHAR(255),
    video_status ENUM('uploading', 'processing', 'ready', 'failed') DEFAULT 'uploading',
    video_quality_levels JSON, -- Available quality levels
    monetization_type ENUM('free', 'subscription', 'ppv_rent', 'ppv_buy', 'hybrid') NOT NULL DEFAULT 'free',
    price_rent DECIMAL(10,2) NULL, -- Rental price
    price_buy DECIMAL(10,2) NULL, -- Purchase price
    rental_duration_hours INT DEFAULT 48, -- How long rental lasts
    currency CHAR(3) DEFAULT 'USD',
    is_featured BOOLEAN DEFAULT FALSE,
    is_trending BOOLEAN DEFAULT FALSE,
    view_count BIGINT UNSIGNED DEFAULT 0,
    like_count BIGINT UNSIGNED DEFAULT 0,
    comment_count BIGINT UNSIGNED DEFAULT 0,
    average_rating DECIMAL(3,2) DEFAULT 0.00,
    rating_count BIGINT UNSIGNED DEFAULT 0,
    status ENUM('draft', 'published', 'scheduled', 'archived', 'private') DEFAULT 'draft',
    published_at TIMESTAMP NULL,
    scheduled_publish_at TIMESTAMP NULL,
    seo_title VARCHAR(255),
    seo_description TEXT,
    tags JSON, -- Array of tags
    cast_crew JSON, -- Cast and crew information
    production_details JSON, -- Production company, year, etc.
    technical_specs JSON, -- Resolution, format, etc.
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (platform_id) REFERENCES filmmaker_platforms(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_content_id) REFERENCES content(id) ON DELETE CASCADE,
    INDEX idx_platform_id (platform_id),
    INDEX idx_content_type (content_type),
    INDEX idx_status (status),
    INDEX idx_monetization_type (monetization_type),
    INDEX idx_published_at (published_at),
    INDEX idx_is_featured (is_featured),
    INDEX idx_is_trending (is_trending),
    INDEX idx_view_count (view_count),
    INDEX idx_slug_platform (slug, platform_id),
    UNIQUE KEY unique_slug_platform (slug, platform_id)
);

-- Content category relationships
CREATE TABLE content_category_relationships (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    content_id BIGINT UNSIGNED NOT NULL,
    category_id BIGINT UNSIGNED NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES content_categories(id) ON DELETE CASCADE,
    UNIQUE KEY unique_content_category (content_id, category_id)
);

-- Subscription plans
CREATE TABLE subscription_plans (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    platform_id BIGINT UNSIGNED NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    currency CHAR(3) DEFAULT 'USD',
    billing_interval ENUM('monthly', 'yearly', 'quarterly') NOT NULL,
    trial_days INT UNSIGNED DEFAULT 0,
    features JSON, -- Array of features included
    max_concurrent_streams INT DEFAULT 1,
    download_enabled BOOLEAN DEFAULT FALSE,
    hd_enabled BOOLEAN DEFAULT TRUE,
    uhd_enabled BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    stripe_price_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (platform_id) REFERENCES filmmaker_platforms(id) ON DELETE CASCADE,
    INDEX idx_platform_id (platform_id),
    INDEX idx_is_active (is_active)
);

-- User subscriptions
CREATE TABLE user_subscriptions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    platform_id BIGINT UNSIGNED NOT NULL,
    plan_id BIGINT UNSIGNED NOT NULL,
    stripe_subscription_id VARCHAR(255),
    status ENUM('active', 'canceled', 'past_due', 'unpaid', 'trialing', 'incomplete') NOT NULL,
    current_period_start TIMESTAMP NOT NULL,
    current_period_end TIMESTAMP NOT NULL,
    trial_start TIMESTAMP NULL,
    trial_end TIMESTAMP NULL,
    canceled_at TIMESTAMP NULL,
    cancellation_reason TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (platform_id) REFERENCES filmmaker_platforms(id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES subscription_plans(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_platform_id (platform_id),
    INDEX idx_status (status),
    INDEX idx_current_period_end (current_period_end),
    UNIQUE KEY unique_user_platform_active (user_id, platform_id, status)
);

-- Pay-per-view purchases/rentals
CREATE TABLE ppv_transactions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    content_id BIGINT UNSIGNED NOT NULL,
    transaction_type ENUM('rent', 'buy') NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency CHAR(3) DEFAULT 'USD',
    stripe_payment_intent_id VARCHAR(255),
    status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    access_expires_at TIMESTAMP NULL, -- For rentals
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_content_id (content_id),
    INDEX idx_status (status),
    INDEX idx_access_expires_at (access_expires_at)
);

-- Platform revenue tracking
CREATE TABLE platform_revenue (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    platform_id BIGINT UNSIGNED NOT NULL,
    revenue_type ENUM('subscription', 'ppv_rent', 'ppv_buy') NOT NULL,
    gross_amount DECIMAL(10,2) NOT NULL,
    platform_commission DECIMAL(10,2) NOT NULL,
    filmmaker_amount DECIMAL(10,2) NOT NULL,
    currency CHAR(3) DEFAULT 'USD',
    stripe_transfer_id VARCHAR(255),
    payout_status ENUM('pending', 'paid', 'failed') DEFAULT 'pending',
    payout_date TIMESTAMP NULL,
    reference_id BIGINT UNSIGNED, -- subscription_id or ppv_transaction_id
    reference_type ENUM('subscription', 'ppv_transaction'),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (platform_id) REFERENCES filmmaker_platforms(id) ON DELETE CASCADE,
    INDEX idx_platform_id (platform_id),
    INDEX idx_revenue_type (revenue_type),
    INDEX idx_payout_status (payout_status),
    INDEX idx_created_at (created_at)
);

-- User content access (for tracking what users can watch)
CREATE TABLE user_content_access (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    content_id BIGINT UNSIGNED NOT NULL,
    access_type ENUM('subscription', 'ppv_rent', 'ppv_buy', 'free') NOT NULL,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,
    subscription_id BIGINT UNSIGNED NULL,
    ppv_transaction_id BIGINT UNSIGNED NULL,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE,
    FOREIGN KEY (subscription_id) REFERENCES user_subscriptions(id) ON DELETE CASCADE,
    FOREIGN KEY (ppv_transaction_id) REFERENCES ppv_transactions(id) ON DELETE CASCADE,
    INDEX idx_user_content (user_id, content_id),
    INDEX idx_expires_at (expires_at),
    UNIQUE KEY unique_user_content_access (user_id, content_id, access_type)
);

-- Video streaming sessions (for analytics and security)
CREATE TABLE streaming_sessions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    content_id BIGINT UNSIGNED NOT NULL,
    session_token VARCHAR(255) NOT NULL UNIQUE,
    ip_address VARCHAR(45),
    user_agent TEXT,
    device_type ENUM('desktop', 'mobile', 'tablet', 'tv', 'unknown') DEFAULT 'unknown',
    quality_level VARCHAR(10), -- 360p, 720p, 1080p, etc.
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_heartbeat TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ended_at TIMESTAMP NULL,
    watch_duration_seconds INT UNSIGNED DEFAULT 0,
    completion_percentage DECIMAL(5,2) DEFAULT 0.00,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_content_id (content_id),
    INDEX idx_session_token (session_token),
    INDEX idx_started_at (started_at)
);

-- User watch history
CREATE TABLE user_watch_history (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    content_id BIGINT UNSIGNED NOT NULL,
    watch_duration_seconds INT UNSIGNED DEFAULT 0,
    completion_percentage DECIMAL(5,2) DEFAULT 0.00,
    last_position_seconds INT UNSIGNED DEFAULT 0,
    device_type ENUM('desktop', 'mobile', 'tablet', 'tv', 'unknown') DEFAULT 'unknown',
    first_watched_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_watched_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_content_id (content_id),
    INDEX idx_last_watched_at (last_watched_at),
    UNIQUE KEY unique_user_content (user_id, content_id)
);

-- Content ratings and reviews
CREATE TABLE content_ratings (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    content_id BIGINT UNSIGNED NOT NULL,
    rating TINYINT UNSIGNED NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review TEXT,
    is_verified_purchase BOOLEAN DEFAULT FALSE,
    helpful_votes INT UNSIGNED DEFAULT 0,
    reported_count INT UNSIGNED DEFAULT 0,
    status ENUM('active', 'hidden', 'reported') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE,
    INDEX idx_content_id (content_id),
    INDEX idx_rating (rating),
    INDEX idx_status (status),
    UNIQUE KEY unique_user_content_rating (user_id, content_id)
);

-- Content comments
CREATE TABLE content_comments (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    content_id BIGINT UNSIGNED NOT NULL,
    parent_comment_id BIGINT UNSIGNED NULL,
    comment TEXT NOT NULL,
    like_count INT UNSIGNED DEFAULT 0,
    reply_count INT UNSIGNED DEFAULT 0,
    reported_count INT UNSIGNED DEFAULT 0,
    status ENUM('active', 'hidden', 'reported') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_comment_id) REFERENCES content_comments(id) ON DELETE CASCADE,
    INDEX idx_content_id (content_id),
    INDEX idx_parent_comment_id (parent_comment_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- User favorites/watchlist
CREATE TABLE user_favorites (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    content_id BIGINT UNSIGNED NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_content_id (content_id),
    UNIQUE KEY unique_user_content_favorite (user_id, content_id)
);

-- Platform followers (users following filmmaker platforms)
CREATE TABLE platform_followers (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    platform_id BIGINT UNSIGNED NOT NULL,
    notification_preferences JSON, -- What notifications they want
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (platform_id) REFERENCES filmmaker_platforms(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_platform_id (platform_id),
    UNIQUE KEY unique_user_platform_follow (user_id, platform_id)
);

-- Notifications
CREATE TABLE notifications (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    type VARCHAR(50) NOT NULL, -- new_content, subscription_expiring, etc.
    title VARCHAR(255) NOT NULL,
    message TEXT,
    data JSON, -- Additional data for the notification
    read_at TIMESTAMP NULL,
    action_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_read_at (read_at),
    INDEX idx_created_at (created_at)
);

-- Analytics events
CREATE TABLE analytics_events (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    platform_id BIGINT UNSIGNED NULL,
    content_id BIGINT UNSIGNED NULL,
    user_id BIGINT UNSIGNED NULL,
    event_type VARCHAR(50) NOT NULL, -- view, like, share, subscribe, etc.
    event_data JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    referrer VARCHAR(500),
    session_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (platform_id) REFERENCES filmmaker_platforms(id) ON DELETE SET NULL,
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_platform_id (platform_id),
    INDEX idx_content_id (content_id),
    INDEX idx_user_id (user_id),
    INDEX idx_event_type (event_type),
    INDEX idx_created_at (created_at)
);

-- System settings
CREATE TABLE system_settings (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_type ENUM('string', 'integer', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE, -- Can be accessed via API
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_setting_key (setting_key),
    INDEX idx_is_public (is_public)
);

-- API tokens (for external integrations)
CREATE TABLE api_tokens (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    name VARCHAR(100) NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    permissions JSON, -- Array of permissions
    last_used_at TIMESTAMP NULL,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_token (token),
    INDEX idx_expires_at (expires_at)
);

-- Failed jobs queue
CREATE TABLE failed_jobs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) NOT NULL UNIQUE,
    connection TEXT NOT NULL,
    queue TEXT NOT NULL,
    payload LONGTEXT NOT NULL,
    exception LONGTEXT NOT NULL,
    failed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_uuid (uuid),
    INDEX idx_failed_at (failed_at)
);

-- Activity logs
CREATE TABLE activity_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NULL,
    action VARCHAR(100) NOT NULL,
    description TEXT,
    subject_type VARCHAR(100), -- Model class name
    subject_id BIGINT UNSIGNED,
    properties JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_subject (subject_type, subject_id),
    INDEX idx_created_at (created_at)
);

-- Insert default data
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('platform_commission_rate', '0.15', 'string', 'Platform commission rate (15%)', FALSE),
('default_currency', 'AUD', 'string', 'Default currency for the platform', TRUE),
('supported_currencies', '["USD","EUR","GBP","CAD","AUD"]', 'json', 'Supported currencies', TRUE),
('max_video_size', '5368709120', 'integer', 'Maximum video file size in bytes (5GB)', FALSE),
('allowed_video_formats', '["mp4","mov","avi","mkv","webm"]', 'json', 'Allowed video formats', TRUE),
('video_quality_levels', '["360p","480p","720p","1080p","4k"]', 'json', 'Available video quality levels', TRUE),
('default_rental_duration', '48', 'integer', 'Default rental duration in hours', TRUE),
('platform_name', 'Filmmaker OTT Platform', 'string', 'Platform name', TRUE),
('platform_description', 'The OTT Platform Built by Filmmakers, for Filmmakers', 'string', 'Platform description', TRUE),
('contact_email', '<EMAIL>', 'string', 'Contact email', TRUE),
('terms_of_service_url', '/terms', 'string', 'Terms of service URL', TRUE),
('privacy_policy_url', '/privacy', 'string', 'Privacy policy URL', TRUE);

-- Insert default content categories
INSERT INTO content_categories (name, slug, description, sort_order) VALUES
('Action', 'action', 'Action and adventure films', 1),
('Comedy', 'comedy', 'Comedy films and series', 2),
('Drama', 'drama', 'Dramatic content', 3),
('Documentary', 'documentary', 'Documentary films and series', 4),
('Horror', 'horror', 'Horror and thriller content', 5),
('Romance', 'romance', 'Romantic films and series', 6),
('Sci-Fi', 'sci-fi', 'Science fiction content', 7),
('Animation', 'animation', 'Animated content', 8),
('Short Films', 'short-films', 'Short form content', 9),
('Music Videos', 'music-videos', 'Music videos and performances', 10),
('Educational', 'educational', 'Educational and instructional content', 11),
('Experimental', 'experimental', 'Experimental and avant-garde films', 12);

SET FOREIGN_KEY_CHECKS = 1;
