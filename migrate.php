<?php

declare(strict_types=1);

/**
 * Simple migration runner
 * Usage: php migrate.php
 */

require_once __DIR__ . '/vendor/autoload.php';

// Load environment variables
if (file_exists(__DIR__ . '/.env')) {
    $dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
    $dotenv->load();
}

// Define constants
define('ROOT_PATH', __DIR__);
define('APP_PATH', ROOT_PATH . '/app');
define('CONFIG_PATH', ROOT_PATH . '/config');
define('STORAGE_PATH', ROOT_PATH . '/storage');

use App\Core\Config;
use App\Core\Database;

try {
    echo "Starting database migration...\n";
    
    $config = new Config();
    $database = new Database($config);
    
    // Test connection
    $connection = $database->getConnection();
    echo "Database connection successful!\n";
    
    // Run migrations
    $database->migrate();
    
    echo "Migration completed successfully!\n";
    
} catch (Exception $e) {
    echo "Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}
