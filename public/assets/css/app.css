/* FilmStream Custom Styles */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
}

::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-gray-500;
}

/* Smooth transitions */
* {
    transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, opacity 0.2s ease, transform 0.2s ease;
}

/* Focus styles */
.focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800;
}

/* Button styles */
.btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md focus-ring disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800;
}

.btn-secondary {
    @apply btn bg-secondary-600 text-white hover:bg-secondary-700 active:bg-secondary-800;
}

.btn-outline {
    @apply btn border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700;
}

.btn-ghost {
    @apply btn text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800;
}

.btn-danger {
    @apply btn bg-red-600 text-white hover:bg-red-700 active:bg-red-800;
}

.btn-sm {
    @apply px-3 py-1.5 text-xs;
}

.btn-lg {
    @apply px-6 py-3 text-base;
}

/* Form styles */
.form-input {
    @apply block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus-ring;
}

.form-textarea {
    @apply form-input resize-none;
}

.form-select {
    @apply form-input pr-10 bg-no-repeat bg-right;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-size: 1.5em 1.5em;
}

.form-checkbox {
    @apply h-4 w-4 text-primary-600 border-gray-300 dark:border-gray-600 rounded focus:ring-primary-500 dark:bg-gray-800;
}

.form-radio {
    @apply h-4 w-4 text-primary-600 border-gray-300 dark:border-gray-600 focus:ring-primary-500 dark:bg-gray-800;
}

.form-label {
    @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1;
}

.form-error {
    @apply text-sm text-red-600 dark:text-red-400 mt-1;
}

.form-help {
    @apply text-sm text-gray-500 dark:text-gray-400 mt-1;
}

/* Card styles */
.card {
    @apply bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700;
}

.card-header {
    @apply px-6 py-4 border-b border-gray-200 dark:border-gray-700;
}

.card-body {
    @apply px-6 py-4;
}

.card-footer {
    @apply px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50;
}

/* Badge styles */
.badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-primary {
    @apply badge bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200;
}

.badge-secondary {
    @apply badge bg-secondary-100 text-secondary-800 dark:bg-secondary-900 dark:text-secondary-200;
}

.badge-success {
    @apply badge bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
}

.badge-warning {
    @apply badge bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200;
}

.badge-danger {
    @apply badge bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200;
}

.badge-gray {
    @apply badge bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300;
}

/* Alert styles */
.alert {
    @apply p-4 rounded-md border;
}

.alert-success {
    @apply alert bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-200;
}

.alert-warning {
    @apply alert bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200;
}

.alert-danger {
    @apply alert bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200;
}

.alert-info {
    @apply alert bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200;
}

/* Loading spinner */
.spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
}

/* Gradient text */
.gradient-text {
    @apply bg-gradient-to-r from-primary-500 to-secondary-500 bg-clip-text text-transparent;
}

/* Hover effects */
.hover-lift {
    @apply transition-transform duration-200 hover:-translate-y-1;
}

.hover-scale {
    @apply transition-transform duration-200 hover:scale-105;
}

/* Glass effect */
.glass {
    @apply backdrop-blur-sm bg-white/80 dark:bg-gray-800/80;
}

/* Custom animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.5s ease-out;
}

.animate-fade-in-down {
    animation: fadeInDown 0.5s ease-out;
}

.animate-slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

/* Responsive utilities */
.container-fluid {
    @apply w-full px-4 sm:px-6 lg:px-8;
}

.container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

/* Video player styles */
.video-container {
    @apply relative w-full h-0 pb-[56.25%] bg-black rounded-lg overflow-hidden;
}

.video-container video {
    @apply absolute top-0 left-0 w-full h-full object-cover;
}

/* File upload styles */
.file-upload {
    @apply relative border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 hover:border-primary-400 dark:hover:border-primary-500 transition-colors;
}

.file-upload.dragover {
    @apply border-primary-500 bg-primary-50 dark:bg-primary-900/20;
}

/* Progress bar */
.progress {
    @apply w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2;
}

.progress-bar {
    @apply bg-primary-600 h-2 rounded-full transition-all duration-300;
}

/* Tooltip */
.tooltip {
    @apply absolute z-50 px-2 py-1 text-xs text-white bg-gray-900 rounded shadow-lg opacity-0 pointer-events-none transition-opacity duration-200;
}

.tooltip.show {
    @apply opacity-100;
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-only {
        display: block !important;
    }
}
