/**
 * FilmStream Core JavaScript
 * Core functionality and utilities
 */

// Global FilmStream object
window.FilmStream = window.FilmStream || {};

// API Helper
window.FilmStreamAPI = {
    async call(endpoint, options = {}) {
        const authToken = localStorage.getItem('authToken');
        const url = window.FilmStream.apiUrl + endpoint;

        const config = {
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': window.FilmStream.csrfToken,
                ...options.headers
            },
            ...options
        };

        if (authToken) {
            config.headers['Authorization'] = `Bearer ${authToken}`;
        }

        try {
            const response = await fetch(url, config);
            const data = await response.json();

            if (!response.ok) {
                if (response.status === 401) {
                    localStorage.removeItem('authToken');
                    localStorage.removeItem('user');
                    if (!window.location.pathname.includes('/login')) {
                        window.location.href = '/login';
                    }
                    return;
                }
                throw new Error(data.message || 'API request failed');
            }

            return data;
        } catch (error) {
            console.error('API Error:', error);
            throw error;
        }
    }
};

// Notification system
window.FilmStream.notify = function(message, type = 'info', duration = 5000) {
    const container = document.getElementById('notifications');
    if (!container) return;

    const notification = document.createElement('div');
    notification.className = `notification max-w-sm w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden transform transition-all duration-300 translate-x-full`;

    const bgColor = {
        success: 'bg-green-500',
        error: 'bg-red-500',
        warning: 'bg-yellow-500',
        info: 'bg-blue-500'
    }[type] || 'bg-blue-500';

    notification.innerHTML = `
        <div class="p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <div class="w-2 h-2 ${bgColor} rounded-full mt-2"></div>
                </div>
                <div class="ml-3 w-0 flex-1 pt-0.5">
                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100">${message}</p>
                </div>
                <div class="ml-4 flex-shrink-0 flex">
                    <button class="bg-white dark:bg-gray-800 rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500" onclick="this.closest('.notification').remove()">
                        <span class="sr-only">Close</span>
                        <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    `;

    container.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Auto remove
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, duration);
};

// Loading overlay
window.FilmStream.loading = {
    show() {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.classList.remove('hidden');
            overlay.classList.add('flex');
        }
    },

    hide() {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.classList.add('hidden');
            overlay.classList.remove('flex');
        }
    }
};

// File upload helper
window.FilmStream.uploadFile = async function(file, endpoint, onProgress = null) {
    const formData = new FormData();
    formData.append('file', file);

    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();

        if (onProgress) {
            xhr.upload.addEventListener('progress', (e) => {
                if (e.lengthComputable) {
                    const percentComplete = (e.loaded / e.total) * 100;
                    onProgress(percentComplete);
                }
            });
        }

        xhr.addEventListener('load', () => {
            if (xhr.status >= 200 && xhr.status < 300) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    resolve(response);
                } catch (e) {
                    reject(new Error('Invalid JSON response'));
                }
            } else {
                try {
                    const error = JSON.parse(xhr.responseText);
                    reject(new Error(error.message || 'Upload failed'));
                } catch (e) {
                    reject(new Error('Upload failed'));
                }
            }
        });

        xhr.addEventListener('error', () => {
            reject(new Error('Upload failed'));
        });

        const authToken = localStorage.getItem('authToken');
        xhr.open('POST', window.FilmStream.apiUrl + endpoint);

        if (authToken) {
            xhr.setRequestHeader('Authorization', `Bearer ${authToken}`);
        }
        xhr.setRequestHeader('X-CSRF-Token', window.FilmStream.csrfToken);

        xhr.send(formData);
    });
};

// Utility functions
window.FilmStream.utils = {
    formatDate(dateString, options = {}) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            ...options
        });
    },

    formatCurrency(amount, currency = 'USD') {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency
        }).format(amount);
    },

    formatNumber(number) {
        return new Intl.NumberFormat('en-US').format(number);
    },

    truncate(text, length = 100) {
        return text.length > length ? text.substring(0, length) + '...' : text;
    },

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
};

// Initialize on DOM ready
document.addEventListener('DOMContentLoaded', function() {
    // Check authentication on protected pages
    const authToken = localStorage.getItem('authToken');
    const isAuthPage = window.location.pathname.includes('/login') ||
                      window.location.pathname.includes('/register');
    const isPublicPage = window.location.pathname === '/' ||
                        window.location.pathname.includes('/discover') ||
                        window.location.pathname.includes('/platform/');

    if (!authToken && !isAuthPage && !isPublicPage) {
        window.location.href = '/login';
    }

    // Update user info if available
    if (authToken && !window.FilmStream.user) {
        const storedUser = localStorage.getItem('user');
        if (storedUser) {
            try {
                window.FilmStream.user = JSON.parse(storedUser);
            } catch (e) {
                console.error('Failed to parse stored user data');
            }
        }
    }
});

/**
 * FilmStream Dashboard JavaScript
 * Handles all dashboard functionality and API interactions
 */

function dashboardApp() {
    return {
        // State
        currentView: 'overview',
        loading: false,
        darkMode: localStorage.getItem('darkMode') === 'true',
        
        // User data
        user: {
            id: null,
            name: '',
            email: '',
            avatar_url: '',
            role: ''
        },
        
        // Dashboard data
        stats: {
            platforms: 0,
            subscribers: 0,
            content: 0,
            revenue: 0
        },
        
        platforms: [],
        recentActivity: [],
        
        // Modal states
        showCreatePlatform: false,
        showEditPlatform: false,
        
        // Form data
        newPlatform: {
            name: '',
            description: '',
            tagline: ''
        },
        
        editingPlatform: null,
        
        // API configuration
        apiBase: '/api',
        authToken: localStorage.getItem('authToken'),
        
        // Initialize the app
        async init() {
            if (!this.authToken) {
                window.location.href = '/login';
                return;
            }
            
            await this.loadUserProfile();
            await this.loadDashboardData();
        },
        
        // API helper methods
        async apiCall(endpoint, options = {}) {
            const url = this.apiBase + endpoint;
            const config = {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.authToken}`,
                    ...options.headers
                },
                ...options
            };
            
            try {
                const response = await fetch(url, config);
                const data = await response.json();
                
                if (!response.ok) {
                    if (response.status === 401) {
                        this.logout();
                        return;
                    }
                    throw new Error(data.message || 'API request failed');
                }
                
                return data;
            } catch (error) {
                console.error('API Error:', error);
                this.showNotification('Error: ' + error.message, 'error');
                throw error;
            }
        },
        
        // Load user profile
        async loadUserProfile() {
            try {
                this.loading = true;
                const response = await this.apiCall('/users/profile');
                this.user = response.data.user;
            } catch (error) {
                console.error('Failed to load user profile:', error);
            } finally {
                this.loading = false;
            }
        },
        
        // Load dashboard data
        async loadDashboardData() {
            try {
                this.loading = true;
                
                // Load platforms
                await this.loadPlatforms();
                
                // Load activity
                await this.loadRecentActivity();
                
                // Calculate stats
                this.calculateStats();
                
            } catch (error) {
                console.error('Failed to load dashboard data:', error);
            } finally {
                this.loading = false;
            }
        },
        
        // Load platforms
        async loadPlatforms() {
            try {
                const response = await this.apiCall('/filmmakers/platforms');
                this.platforms = response.data.platforms || [];
            } catch (error) {
                console.error('Failed to load platforms:', error);
                this.platforms = [];
            }
        },
        
        // Load recent activity
        async loadRecentActivity() {
            try {
                const response = await this.apiCall('/users/activity?per_page=10');
                this.recentActivity = response.data.items || [];
            } catch (error) {
                console.error('Failed to load activity:', error);
                this.recentActivity = [];
            }
        },
        
        // Calculate dashboard stats
        calculateStats() {
            this.stats.platforms = this.platforms.length;
            this.stats.subscribers = this.platforms.reduce((total, platform) => {
                return total + (platform.subscribers_count || 0);
            }, 0);
            this.stats.content = this.platforms.reduce((total, platform) => {
                return total + (platform.content_count || 0);
            }, 0);
            this.stats.revenue = this.platforms.reduce((total, platform) => {
                return total + (platform.monthly_revenue || 0);
            }, 0);
        },
        
        // Create new platform
        async createPlatform() {
            try {
                this.loading = true;
                
                const response = await this.apiCall('/platforms', {
                    method: 'POST',
                    body: JSON.stringify(this.newPlatform)
                });
                
                this.platforms.push(response.data.platform);
                this.showCreatePlatform = false;
                this.newPlatform = { name: '', description: '', tagline: '' };
                this.calculateStats();
                
                this.showNotification('Platform created successfully!', 'success');
                
            } catch (error) {
                console.error('Failed to create platform:', error);
            } finally {
                this.loading = false;
            }
        },
        
        // Edit platform
        editPlatform(platform) {
            this.editingPlatform = { ...platform };
            this.showEditPlatform = true;
        },
        
        // Update platform
        async updatePlatform() {
            try {
                this.loading = true;
                
                const response = await this.apiCall(`/platforms/${this.editingPlatform.id}`, {
                    method: 'PUT',
                    body: JSON.stringify(this.editingPlatform)
                });
                
                // Update platform in list
                const index = this.platforms.findIndex(p => p.id === this.editingPlatform.id);
                if (index !== -1) {
                    this.platforms[index] = response.data.platform;
                }
                
                this.showEditPlatform = false;
                this.editingPlatform = null;
                
                this.showNotification('Platform updated successfully!', 'success');
                
            } catch (error) {
                console.error('Failed to update platform:', error);
            } finally {
                this.loading = false;
            }
        },
        
        // View platform
        viewPlatform(platform) {
            window.open(platform.url, '_blank');
        },
        
        // Delete platform
        async deletePlatform(platform) {
            if (!confirm('Are you sure you want to delete this platform? This action cannot be undone.')) {
                return;
            }
            
            try {
                this.loading = true;
                
                await this.apiCall(`/platforms/${platform.id}`, {
                    method: 'DELETE'
                });
                
                this.platforms = this.platforms.filter(p => p.id !== platform.id);
                this.calculateStats();
                
                this.showNotification('Platform deleted successfully!', 'success');
                
            } catch (error) {
                console.error('Failed to delete platform:', error);
            } finally {
                this.loading = false;
            }
        },
        
        // Toggle dark mode
        toggleDarkMode() {
            this.darkMode = !this.darkMode;
            localStorage.setItem('darkMode', this.darkMode.toString());
        },
        
        // Logout
        async logout() {
            try {
                await this.apiCall('/auth/logout', { method: 'POST' });
            } catch (error) {
                console.error('Logout error:', error);
            } finally {
                localStorage.removeItem('authToken');
                localStorage.removeItem('user');
                window.location.href = '/login';
            }
        },
        
        // Utility methods
        formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
        },
        
        formatCurrency(amount) {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD'
            }).format(amount);
        },
        
        // Show notification
        showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg max-w-sm ${
                type === 'success' ? 'bg-green-500 text-white' :
                type === 'error' ? 'bg-red-500 text-white' :
                'bg-blue-500 text-white'
            }`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            // Remove after 5 seconds
            setTimeout(() => {
                notification.remove();
            }, 5000);
        },
        
        // File upload helper
        async uploadFile(file, endpoint) {
            const formData = new FormData();
            formData.append('file', file);
            
            return await this.apiCall(endpoint, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.authToken}`
                    // Don't set Content-Type for FormData
                },
                body: formData
            });
        },
        
        // Handle file input change
        async handleFileUpload(event, type, platformId) {
            const file = event.target.files[0];
            if (!file) return;
            
            try {
                this.loading = true;
                
                const endpoint = `/platforms/${platformId}/${type}`;
                const response = await this.uploadFile(file, endpoint);
                
                // Update platform in list
                const platform = this.platforms.find(p => p.id === platformId);
                if (platform) {
                    platform[`${type}_url`] = response.data[`${type}_url`];
                }
                
                this.showNotification(`${type.charAt(0).toUpperCase() + type.slice(1)} uploaded successfully!`, 'success');
                
            } catch (error) {
                console.error(`Failed to upload ${type}:`, error);
            } finally {
                this.loading = false;
            }
        }
    };
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Check for auth token on protected pages
    const authToken = localStorage.getItem('authToken');
    const isAuthPage = window.location.pathname.includes('/login') || window.location.pathname.includes('/register');
    
    if (!authToken && !isAuthPage) {
        window.location.href = '/login';
    }
});

// Global API helper for non-Alpine components
window.FilmStreamAPI = {
    async call(endpoint, options = {}) {
        const authToken = localStorage.getItem('authToken');
        const url = '/api' + endpoint;
        
        const config = {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`,
                ...options.headers
            },
            ...options
        };
        
        try {
            const response = await fetch(url, config);
            const data = await response.json();
            
            if (!response.ok) {
                if (response.status === 401) {
                    localStorage.removeItem('authToken');
                    window.location.href = '/login';
                    return;
                }
                throw new Error(data.message || 'API request failed');
            }
            
            return data;
        } catch (error) {
            console.error('API Error:', error);
            throw error;
        }
    }
};
