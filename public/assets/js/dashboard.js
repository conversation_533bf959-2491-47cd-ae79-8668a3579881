/**
 * FilmStream Dashboard JavaScript
 * Handles all dashboard functionality and API interactions
 */

function dashboardApp() {
    return {
        // State
        currentView: 'overview',
        loading: false,
        darkMode: localStorage.getItem('darkMode') === 'true',
        
        // User data
        user: {
            id: null,
            name: '',
            email: '',
            avatar_url: '',
            role: ''
        },
        
        // Dashboard data
        stats: {
            platforms: 0,
            subscribers: 0,
            content: 0,
            revenue: 0
        },
        
        platforms: [],
        recentActivity: [],
        
        // Modal states
        showCreatePlatform: false,
        showEditPlatform: false,
        
        // Form data
        newPlatform: {
            name: '',
            description: '',
            tagline: ''
        },
        
        editingPlatform: null,
        
        // API configuration
        apiBase: '/api',
        authToken: localStorage.getItem('authToken'),
        
        // Initialize the app
        async init() {
            if (!this.authToken) {
                window.location.href = '/login';
                return;
            }
            
            await this.loadUserProfile();
            await this.loadDashboardData();
        },
        
        // API helper methods
        async apiCall(endpoint, options = {}) {
            const url = this.apiBase + endpoint;
            const config = {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.authToken}`,
                    ...options.headers
                },
                ...options
            };
            
            try {
                const response = await fetch(url, config);
                const data = await response.json();
                
                if (!response.ok) {
                    if (response.status === 401) {
                        this.logout();
                        return;
                    }
                    throw new Error(data.message || 'API request failed');
                }
                
                return data;
            } catch (error) {
                console.error('API Error:', error);
                this.showNotification('Error: ' + error.message, 'error');
                throw error;
            }
        },
        
        // Load user profile
        async loadUserProfile() {
            try {
                this.loading = true;
                const response = await this.apiCall('/users/profile');
                this.user = response.data.user;
            } catch (error) {
                console.error('Failed to load user profile:', error);
            } finally {
                this.loading = false;
            }
        },
        
        // Load dashboard data
        async loadDashboardData() {
            try {
                this.loading = true;
                
                // Load platforms
                await this.loadPlatforms();
                
                // Load activity
                await this.loadRecentActivity();
                
                // Calculate stats
                this.calculateStats();
                
            } catch (error) {
                console.error('Failed to load dashboard data:', error);
            } finally {
                this.loading = false;
            }
        },
        
        // Load platforms
        async loadPlatforms() {
            try {
                const response = await this.apiCall('/filmmakers/platforms');
                this.platforms = response.data.platforms || [];
            } catch (error) {
                console.error('Failed to load platforms:', error);
                this.platforms = [];
            }
        },
        
        // Load recent activity
        async loadRecentActivity() {
            try {
                const response = await this.apiCall('/users/activity?per_page=10');
                this.recentActivity = response.data.items || [];
            } catch (error) {
                console.error('Failed to load activity:', error);
                this.recentActivity = [];
            }
        },
        
        // Calculate dashboard stats
        calculateStats() {
            this.stats.platforms = this.platforms.length;
            this.stats.subscribers = this.platforms.reduce((total, platform) => {
                return total + (platform.subscribers_count || 0);
            }, 0);
            this.stats.content = this.platforms.reduce((total, platform) => {
                return total + (platform.content_count || 0);
            }, 0);
            this.stats.revenue = this.platforms.reduce((total, platform) => {
                return total + (platform.monthly_revenue || 0);
            }, 0);
        },
        
        // Create new platform
        async createPlatform() {
            try {
                this.loading = true;
                
                const response = await this.apiCall('/platforms', {
                    method: 'POST',
                    body: JSON.stringify(this.newPlatform)
                });
                
                this.platforms.push(response.data.platform);
                this.showCreatePlatform = false;
                this.newPlatform = { name: '', description: '', tagline: '' };
                this.calculateStats();
                
                this.showNotification('Platform created successfully!', 'success');
                
            } catch (error) {
                console.error('Failed to create platform:', error);
            } finally {
                this.loading = false;
            }
        },
        
        // Edit platform
        editPlatform(platform) {
            this.editingPlatform = { ...platform };
            this.showEditPlatform = true;
        },
        
        // Update platform
        async updatePlatform() {
            try {
                this.loading = true;
                
                const response = await this.apiCall(`/platforms/${this.editingPlatform.id}`, {
                    method: 'PUT',
                    body: JSON.stringify(this.editingPlatform)
                });
                
                // Update platform in list
                const index = this.platforms.findIndex(p => p.id === this.editingPlatform.id);
                if (index !== -1) {
                    this.platforms[index] = response.data.platform;
                }
                
                this.showEditPlatform = false;
                this.editingPlatform = null;
                
                this.showNotification('Platform updated successfully!', 'success');
                
            } catch (error) {
                console.error('Failed to update platform:', error);
            } finally {
                this.loading = false;
            }
        },
        
        // View platform
        viewPlatform(platform) {
            window.open(platform.url, '_blank');
        },
        
        // Delete platform
        async deletePlatform(platform) {
            if (!confirm('Are you sure you want to delete this platform? This action cannot be undone.')) {
                return;
            }
            
            try {
                this.loading = true;
                
                await this.apiCall(`/platforms/${platform.id}`, {
                    method: 'DELETE'
                });
                
                this.platforms = this.platforms.filter(p => p.id !== platform.id);
                this.calculateStats();
                
                this.showNotification('Platform deleted successfully!', 'success');
                
            } catch (error) {
                console.error('Failed to delete platform:', error);
            } finally {
                this.loading = false;
            }
        },
        
        // Toggle dark mode
        toggleDarkMode() {
            this.darkMode = !this.darkMode;
            localStorage.setItem('darkMode', this.darkMode.toString());
        },
        
        // Logout
        async logout() {
            try {
                await this.apiCall('/auth/logout', { method: 'POST' });
            } catch (error) {
                console.error('Logout error:', error);
            } finally {
                localStorage.removeItem('authToken');
                localStorage.removeItem('user');
                window.location.href = '/login';
            }
        },
        
        // Utility methods
        formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
        },
        
        formatCurrency(amount) {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD'
            }).format(amount);
        },
        
        // Show notification
        showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg max-w-sm ${
                type === 'success' ? 'bg-green-500 text-white' :
                type === 'error' ? 'bg-red-500 text-white' :
                'bg-blue-500 text-white'
            }`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            // Remove after 5 seconds
            setTimeout(() => {
                notification.remove();
            }, 5000);
        },
        
        // File upload helper
        async uploadFile(file, endpoint) {
            const formData = new FormData();
            formData.append('file', file);
            
            return await this.apiCall(endpoint, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.authToken}`
                    // Don't set Content-Type for FormData
                },
                body: formData
            });
        },
        
        // Handle file input change
        async handleFileUpload(event, type, platformId) {
            const file = event.target.files[0];
            if (!file) return;
            
            try {
                this.loading = true;
                
                const endpoint = `/platforms/${platformId}/${type}`;
                const response = await this.uploadFile(file, endpoint);
                
                // Update platform in list
                const platform = this.platforms.find(p => p.id === platformId);
                if (platform) {
                    platform[`${type}_url`] = response.data[`${type}_url`];
                }
                
                this.showNotification(`${type.charAt(0).toUpperCase() + type.slice(1)} uploaded successfully!`, 'success');
                
            } catch (error) {
                console.error(`Failed to upload ${type}:`, error);
            } finally {
                this.loading = false;
            }
        }
    };
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Check for auth token on protected pages
    const authToken = localStorage.getItem('authToken');
    const isAuthPage = window.location.pathname.includes('/login') || window.location.pathname.includes('/register');
    
    if (!authToken && !isAuthPage) {
        window.location.href = '/login';
    }
});

// Global API helper for non-Alpine components
window.FilmStreamAPI = {
    async call(endpoint, options = {}) {
        const authToken = localStorage.getItem('authToken');
        const url = '/api' + endpoint;
        
        const config = {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`,
                ...options.headers
            },
            ...options
        };
        
        try {
            const response = await fetch(url, config);
            const data = await response.json();
            
            if (!response.ok) {
                if (response.status === 401) {
                    localStorage.removeItem('authToken');
                    window.location.href = '/login';
                    return;
                }
                throw new Error(data.message || 'API request failed');
            }
            
            return data;
        } catch (error) {
            console.error('API Error:', error);
            throw error;
        }
    }
};
