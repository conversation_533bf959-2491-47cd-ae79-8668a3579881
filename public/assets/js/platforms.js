/**
 * FilmStream Platforms JavaScript
 * Handles platform management functionality
 */

function platformsApp() {
    return {
        // State
        loading: false,
        platforms: [],
        
        // Modals
        showCreateModal: false,
        showEditModal: false,
        
        // Forms
        newPlatform: {
            name: '',
            description: '',
            tagline: '',
            subscription_enabled: true,
            ppv_enabled: true,
            free_content_enabled: true
        },
        
        editingPlatform: null,
        
        // Initialize
        async init() {
            await this.loadPlatforms();
        },
        
        // Load platforms
        async loadPlatforms() {
            try {
                this.loading = true;
                const response = await FilmStreamAPI.call('/filmmakers/platforms');
                this.platforms = response.data.platforms || [];
            } catch (error) {
                console.error('Failed to load platforms:', error);
                FilmStream.notify('Failed to load platforms: ' + error.message, 'error');
                this.platforms = [];
            } finally {
                this.loading = false;
            }
        },
        
        // Create platform
        async createPlatform() {
            try {
                this.loading = true;
                
                const response = await FilmStreamAPI.call('/platforms', {
                    method: 'POST',
                    body: JSON.stringify(this.newPlatform)
                });
                
                this.platforms.push(response.data.platform);
                this.showCreateModal = false;
                this.resetNewPlatform();
                
                FilmStream.notify('Platform created successfully!', 'success');
                
            } catch (error) {
                console.error('Failed to create platform:', error);
                FilmStream.notify('Failed to create platform: ' + error.message, 'error');
            } finally {
                this.loading = false;
            }
        },
        
        // Edit platform
        editPlatform(platform) {
            this.editingPlatform = { ...platform };
            this.showEditModal = true;
        },
        
        // Update platform
        async updatePlatform() {
            try {
                this.loading = true;
                
                const response = await FilmStreamAPI.call(`/platforms/${this.editingPlatform.id}`, {
                    method: 'PUT',
                    body: JSON.stringify(this.editingPlatform)
                });
                
                // Update platform in list
                const index = this.platforms.findIndex(p => p.id === this.editingPlatform.id);
                if (index !== -1) {
                    this.platforms[index] = response.data.platform;
                }
                
                this.showEditModal = false;
                this.editingPlatform = null;
                
                FilmStream.notify('Platform updated successfully!', 'success');
                
            } catch (error) {
                console.error('Failed to update platform:', error);
                FilmStream.notify('Failed to update platform: ' + error.message, 'error');
            } finally {
                this.loading = false;
            }
        },
        
        // View platform
        viewPlatform(platform) {
            window.open(platform.url, '_blank');
        },
        
        // Show analytics
        showAnalytics(platform) {
            window.location.href = `/dashboard/platforms/${platform.id}/analytics`;
        },
        
        // Delete platform
        async deletePlatform(platform) {
            if (!confirm('Are you sure you want to delete this platform? This action cannot be undone.')) {
                return;
            }
            
            try {
                this.loading = true;
                
                await FilmStreamAPI.call(`/platforms/${platform.id}`, {
                    method: 'DELETE'
                });
                
                this.platforms = this.platforms.filter(p => p.id !== platform.id);
                
                FilmStream.notify('Platform deleted successfully!', 'success');
                
            } catch (error) {
                console.error('Failed to delete platform:', error);
                FilmStream.notify('Failed to delete platform: ' + error.message, 'error');
            } finally {
                this.loading = false;
            }
        },
        
        // Handle file upload
        async handleFileUpload(event, type) {
            const file = event.target.files[0];
            if (!file) return;
            
            if (!this.editingPlatform) return;
            
            try {
                this.loading = true;
                
                const endpoint = `/platforms/${this.editingPlatform.id}/${type}`;
                const response = await FilmStream.uploadFile(file, endpoint, (progress) => {
                    console.log(`Upload progress: ${progress}%`);
                });
                
                // Update the editing platform
                this.editingPlatform[`${type}_url`] = response.data[`${type}_url`];
                
                // Update in platforms list
                const platform = this.platforms.find(p => p.id === this.editingPlatform.id);
                if (platform) {
                    platform[`${type}_url`] = response.data[`${type}_url`];
                }
                
                FilmStream.notify(`${type.charAt(0).toUpperCase() + type.slice(1)} uploaded successfully!`, 'success');
                
            } catch (error) {
                console.error(`Failed to upload ${type}:`, error);
                FilmStream.notify(`Failed to upload ${type}: ` + error.message, 'error');
            } finally {
                this.loading = false;
            }
        },
        
        // Handle file drop
        handleFileDrop(event, type) {
            const files = event.dataTransfer.files;
            if (files.length > 0) {
                const file = files[0];
                if (file.type.startsWith('image/')) {
                    // Create a fake event object
                    const fakeEvent = {
                        target: {
                            files: [file]
                        }
                    };
                    this.handleFileUpload(fakeEvent, type);
                } else {
                    FilmStream.notify('Please upload an image file', 'error');
                }
            }
        },
        
        // Reset new platform form
        resetNewPlatform() {
            this.newPlatform = {
                name: '',
                description: '',
                tagline: '',
                subscription_enabled: true,
                ppv_enabled: true,
                free_content_enabled: true
            };
        },
        
        // Get platform status badge class
        getStatusBadgeClass(status) {
            switch (status) {
                case 'active':
                    return 'badge-success';
                case 'inactive':
                    return 'badge-danger';
                case 'draft':
                    return 'badge-warning';
                default:
                    return 'badge-gray';
            }
        },
        
        // Format platform stats
        formatStats(platform) {
            return {
                subscribers: FilmStream.utils.formatNumber(platform.subscribers_count || 0),
                content: FilmStream.utils.formatNumber(platform.content_count || 0),
                revenue: FilmStream.utils.formatCurrency(platform.monthly_revenue || 0)
            };
        }
    };
}

// Platform management utilities
window.PlatformUtils = {
    // Validate platform name
    validateName(name) {
        if (!name || name.trim().length < 3) {
            return 'Platform name must be at least 3 characters long';
        }
        if (name.length > 255) {
            return 'Platform name must be less than 255 characters';
        }
        return null;
    },
    
    // Generate slug from name
    generateSlug(name) {
        return name
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/^-+|-+$/g, '');
    },
    
    // Validate image file
    validateImage(file) {
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        const maxSize = 5 * 1024 * 1024; // 5MB
        
        if (!allowedTypes.includes(file.type)) {
            return 'Please upload a valid image file (JPEG, PNG, GIF, or WebP)';
        }
        
        if (file.size > maxSize) {
            return 'Image file must be less than 5MB';
        }
        
        return null;
    },
    
    // Get platform URL
    getPlatformUrl(platform) {
        if (platform.custom_domain) {
            return `https://${platform.custom_domain}`;
        }
        return `${window.FilmStream.baseUrl}/platform/${platform.slug}`;
    }
};
