<?php

declare(strict_types=1);

/**
 * FilmStream OTT Platform
 * Entry point for the application
 */

// Define application constants
define('APP_START', microtime(true));
define('ROOT_PATH', dirname(__DIR__));
define('APP_PATH', ROOT_PATH . '/app');
define('CONFIG_PATH', ROOT_PATH . '/config');
define('STORAGE_PATH', ROOT_PATH . '/storage');
define('PUBLIC_PATH', __DIR__);

// Error reporting for development
if (file_exists(ROOT_PATH . '/.env')) {
    $env = parse_ini_file(ROOT_PATH . '/.env');
    if (isset($env['APP_DEBUG']) && $env['APP_DEBUG'] === 'true') {
        error_reporting(E_ALL);
        ini_set('display_errors', '1');
    }
}

// Autoload dependencies
require_once ROOT_PATH . '/vendor/autoload.php';

// Load environment variables
if (file_exists(ROOT_PATH . '/.env')) {
    $dotenv = Dotenv\Dotenv::createImmutable(ROOT_PATH);
    $dotenv->load();
}

// Bootstrap the application
try {
    $app = require_once ROOT_PATH . '/bootstrap/app.php';
    
    // Handle the request
    $response = $app->handle();
    
    // Send the response
    $response->send();
    
} catch (Throwable $e) {
    // Handle uncaught exceptions
    http_response_code(500);
    
    if ($_ENV['APP_DEBUG'] ?? false) {
        echo '<h1>Application Error</h1>';
        echo '<p><strong>Message:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '<p><strong>File:</strong> ' . htmlspecialchars($e->getFile()) . '</p>';
        echo '<p><strong>Line:</strong> ' . $e->getLine() . '</p>';
        echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
    } else {
        echo '<h1>500 - Internal Server Error</h1>';
        echo '<p>Something went wrong. Please try again later.</p>';
    }
    
    // Log the error
    error_log(sprintf(
        "[%s] %s in %s:%d\nStack trace:\n%s",
        date('Y-m-d H:i:s'),
        $e->getMessage(),
        $e->getFile(),
        $e->getLine(),
        $e->getTraceAsString()
    ));
}
