

    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="mx-auto h-12 w-auto flex justify-center">
                <h1 class="text-4xl font-bold gradient-text">
                    FilmStream
                </h1>
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-gray-100">
                Sign in to your account
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">
                Or
                <a href="<?= $this->url('register') ?>" class="font-medium text-primary-600 hover:text-primary-500">
                    create a new account
                </a>
            </p>
        </div>
        
        <form class="mt-8 space-y-6" @submit.prevent="login()">
            <div class="rounded-md shadow-sm -space-y-px">
                <div>
                    <label for="email" class="sr-only">Email address</label>
                    <input id="email" name="email" type="email" autocomplete="email" required
                           x-model="form.email"
                           class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-gray-100 dark:bg-gray-800 rounded-t-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                           placeholder="Email address">
                </div>
                <div>
                    <label for="password" class="sr-only">Password</label>
                    <input id="password" name="password" type="password" autocomplete="current-password" required
                           x-model="form.password"
                           class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-gray-100 dark:bg-gray-800 rounded-b-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                           placeholder="Password">
                </div>
            </div>

            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <input id="remember-me" name="remember-me" type="checkbox" 
                           x-model="form.remember_me"
                           class="form-checkbox">
                    <label for="remember-me" class="ml-2 block text-sm text-gray-900 dark:text-gray-100">
                        Remember me
                    </label>
                </div>

                <div class="text-sm">
                    <a href="<?= $this->url('forgot-password') ?>" class="font-medium text-primary-600 hover:text-primary-500">
                        Forgot your password?
                    </a>
                </div>
            </div>

            <!-- Error message -->
            <div x-show="error" x-transition class="alert-danger">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm" x-text="error"></p>
                    </div>
                </div>
            </div>

            <!-- 2FA Code Input -->
            <div x-show="requires2FA" x-transition class="space-y-4">
                <div>
                    <label for="twofa-code" class="form-label">
                        Two-Factor Authentication Code
                    </label>
                    <input id="twofa-code" name="twofa-code" type="text" 
                           x-model="form.twofa_code"
                           class="form-input"
                           placeholder="Enter 6-digit code">
                </div>
                <button type="button" @click="verify2FA()"
                        :disabled="loading"
                        class="btn-primary w-full">
                    <span x-show="!loading">Verify Code</span>
                    <span x-show="loading" class="flex items-center justify-center">
                        <div class="spinner w-5 h-5 mr-2"></div>
                        Verifying...
                    </span>
                </button>
            </div>

            <div x-show="!requires2FA">
                <button type="submit" 
                        :disabled="loading"
                        class="btn-primary w-full">
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <svg class="h-5 w-5 text-primary-500 group-hover:text-primary-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                        </svg>
                    </span>
                    <span x-show="!loading">Sign in</span>
                    <span x-show="loading" class="flex items-center justify-center">
                        <div class="spinner w-5 h-5 mr-2"></div>
                        Signing in...
                    </span>
                </button>
            </div>
        </form>

        <!-- Dark mode toggle -->
        <div class="flex justify-center">
            <button @click="toggleDarkMode()" 
                    class="p-2 rounded-md text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
                <svg x-show="!darkMode" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                </svg>
                <svg x-show="darkMode" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
            </button>
        </div>
    </div>
</div>

<script>
function loginApp() {
    return {
        loading: false,
        error: '',
        requires2FA: false,
        darkMode: localStorage.getItem('darkMode') === 'true',

        form: {
            email: '',
            password: '',
            remember_me: false,
            twofa_code: ''
        },

        init() {
            // Check if already logged in
            if (localStorage.getItem('authToken')) {
                window.location.href = '/dashboard';
            }
        },

        async login() {
            this.loading = true;
            this.error = '';

            try {
                const response = await FilmStreamAPI.call('/auth/login', {
                    method: 'POST',
                    body: JSON.stringify({
                        email: this.form.email,
                        password: this.form.password,
                        remember_me: this.form.remember_me
                    })
                });

                if (response.data.requires_2fa) {
                    this.requires2FA = true;
                    return;
                }

                // Store auth token and user data
                localStorage.setItem('authToken', response.data.tokens.access_token);
                localStorage.setItem('user', JSON.stringify(response.data.user));

                // Redirect to dashboard
                window.location.href = '/dashboard';

            } catch (error) {
                this.error = error.message;
            } finally {
                this.loading = false;
            }
        },

        async verify2FA() {
            this.loading = true;
            this.error = '';

            try {
                const response = await FilmStreamAPI.call('/auth/2fa/verify', {
                    method: 'POST',
                    body: JSON.stringify({
                        code: this.form.twofa_code
                    })
                });

                // Store auth token and user data
                localStorage.setItem('authToken', response.data.tokens.access_token);
                localStorage.setItem('user', JSON.stringify(response.data.user));

                // Redirect to dashboard
                window.location.href = '/dashboard';

            } catch (error) {
                this.error = error.message;
            } finally {
                this.loading = false;
            }
        },

        toggleDarkMode() {
            this.darkMode = !this.darkMode;
            localStorage.setItem('darkMode', this.darkMode.toString());
        }
    };
}
</script>
