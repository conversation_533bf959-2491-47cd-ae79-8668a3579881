<div x-data="registerApp()" x-init="init()" class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="mx-auto h-12 w-auto flex justify-center">
                <h1 class="text-4xl font-bold gradient-text">
                    FilmStream
                </h1>
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-gray-100">
                Create your account
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">
                Or
                <a href="<?= $this->url('login') ?>" class="font-medium text-primary-600 hover:text-primary-500">
                    sign in to your existing account
                </a>
            </p>
        </div>
        
        <form class="mt-8 space-y-6" @submit.prevent="register()">
            <div class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label for="first_name" class="form-label">
                            First Name
                        </label>
                        <input id="first_name" name="first_name" type="text" required
                               x-model="form.first_name"
                               class="form-input"
                               placeholder="First name">
                    </div>
                    <div>
                        <label for="last_name" class="form-label">
                            Last Name
                        </label>
                        <input id="last_name" name="last_name" type="text" required
                               x-model="form.last_name"
                               class="form-input"
                               placeholder="Last name">
                    </div>
                </div>
                
                <div>
                    <label for="email" class="form-label">
                        Email Address
                    </label>
                    <input id="email" name="email" type="email" autocomplete="email" required
                           x-model="form.email"
                           class="form-input"
                           placeholder="Email address">
                </div>
                
                <div>
                    <label for="role" class="form-label">
                        Account Type
                    </label>
                    <select id="role" name="role" required x-model="form.role" class="form-select">
                        <option value="">Select account type</option>
                        <option value="viewer">Viewer - Watch content from filmmakers</option>
                        <option value="filmmaker">Filmmaker - Create and manage your own platform</option>
                    </select>
                </div>
                
                <div>
                    <label for="password" class="form-label">
                        Password
                    </label>
                    <input id="password" name="password" type="password" autocomplete="new-password" required
                           x-model="form.password"
                           class="form-input"
                           placeholder="Password (min. 8 characters)">
                </div>
                
                <div>
                    <label for="password_confirmation" class="form-label">
                        Confirm Password
                    </label>
                    <input id="password_confirmation" name="password_confirmation" type="password" required
                           x-model="form.password_confirmation"
                           class="form-input"
                           placeholder="Confirm password">
                </div>
            </div>

            <div class="flex items-center">
                <input id="terms_accepted" name="terms_accepted" type="checkbox" required
                       x-model="form.terms_accepted"
                       class="form-checkbox">
                <label for="terms_accepted" class="ml-2 block text-sm text-gray-900 dark:text-gray-100">
                    I agree to the 
                    <a href="<?= $this->url('terms') ?>" class="text-primary-600 hover:text-primary-500">Terms of Service</a>
                    and 
                    <a href="<?= $this->url('privacy') ?>" class="text-primary-600 hover:text-primary-500">Privacy Policy</a>
                </label>
            </div>

            <!-- Error message -->
            <div x-show="error" x-transition class="alert-danger">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm" x-text="error"></p>
                    </div>
                </div>
            </div>

            <!-- Success message -->
            <div x-show="success" x-transition class="alert-success">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm">
                            Registration successful! Please check your email to verify your account.
                        </p>
                    </div>
                </div>
            </div>

            <div>
                <button type="submit" 
                        :disabled="loading"
                        class="btn-primary w-full">
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <svg class="h-5 w-5 text-primary-500 group-hover:text-primary-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z" />
                        </svg>
                    </span>
                    <span x-show="!loading">Create Account</span>
                    <span x-show="loading" class="flex items-center justify-center">
                        <div class="spinner w-5 h-5 mr-2"></div>
                        Creating Account...
                    </span>
                </button>
            </div>
        </form>

        <!-- Dark mode toggle -->
        <div class="flex justify-center">
            <button @click="toggleDarkMode()" 
                    class="p-2 rounded-md text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
                <svg x-show="!darkMode" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                </svg>
                <svg x-show="darkMode" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
            </button>
        </div>
    </div>
</div>

<script>
function registerApp() {
    return {
        loading: false,
        error: '',
        success: false,
        darkMode: localStorage.getItem('darkMode') === 'true',
        
        form: {
            first_name: '',
            last_name: '',
            email: '',
            role: '',
            password: '',
            password_confirmation: '',
            terms_accepted: false
        },
        
        init() {
            // Check if already logged in
            if (localStorage.getItem('authToken')) {
                window.location.href = '/dashboard';
            }
        },
        
        async register() {
            this.loading = true;
            this.error = '';
            this.success = false;
            
            // Validate passwords match
            if (this.form.password !== this.form.password_confirmation) {
                this.error = 'Passwords do not match';
                this.loading = false;
                return;
            }
            
            try {
                const response = await FilmStreamAPI.call('/auth/register', {
                    method: 'POST',
                    body: JSON.stringify(this.form)
                });
                
                this.success = true;
                
                // Redirect to login after 3 seconds
                setTimeout(() => {
                    window.location.href = '/login';
                }, 3000);
                
            } catch (error) {
                this.error = error.message;
            } finally {
                this.loading = false;
            }
        },
        
        toggleDarkMode() {
            this.darkMode = !this.darkMode;
            localStorage.setItem('darkMode', this.darkMode.toString());
        }
    };
}
</script>
