<div x-data="platformsApp()" x-init="init()" class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="container py-6">
            <div class="md:flex md:items-center md:justify-between">
                <div class="flex-1 min-w-0">
                    <h2 class="text-2xl font-bold leading-7 text-gray-900 dark:text-gray-100 sm:text-3xl sm:truncate">
                        My Platforms
                    </h2>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        Create and manage your streaming platforms
                    </p>
                </div>
                <div class="mt-4 flex md:mt-0 md:ml-4">
                    <button @click="showCreateModal = true" class="btn-primary">
                        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Create Platform
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="container py-8">
        <!-- Loading State -->
        <div x-show="loading" class="flex justify-center items-center py-12">
            <div class="spinner w-12 h-12"></div>
        </div>

        <!-- Empty State -->
        <div x-show="!loading && platforms.length === 0" class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No platforms</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by creating your first platform.</p>
            <div class="mt-6">
                <button @click="showCreateModal = true" class="btn-primary">
                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Create Platform
                </button>
            </div>
        </div>

        <!-- Platforms Grid -->
        <div x-show="!loading && platforms.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <template x-for="platform in platforms" :key="platform.id">
                <div class="card hover-lift">
                    <!-- Platform Banner -->
                    <div class="aspect-w-16 aspect-h-9">
                        <img :src="platform.banner_url || '/assets/images/default-banner.jpg'" 
                             :alt="platform.name" 
                             class="w-full h-48 object-cover rounded-t-lg">
                    </div>
                    
                    <!-- Platform Info -->
                    <div class="card-body">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2" x-text="platform.name"></h3>
                                <p class="text-sm text-gray-500 dark:text-gray-400 mb-4" x-text="FilmStream.utils.truncate(platform.description, 100)"></p>
                            </div>
                            <div class="ml-4">
                                <span :class="platform.status === 'active' ? 'badge-success' : 'badge-warning'" 
                                      x-text="platform.status" class="badge"></span>
                            </div>
                        </div>
                        
                        <!-- Stats -->
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div class="text-center">
                                <div class="text-lg font-semibold text-gray-900 dark:text-gray-100" x-text="FilmStream.utils.formatNumber(platform.subscribers_count)"></div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">Subscribers</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-semibold text-gray-900 dark:text-gray-100" x-text="FilmStream.utils.formatNumber(platform.content_count || 0)"></div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">Content</div>
                            </div>
                        </div>
                        
                        <!-- Actions -->
                        <div class="flex space-x-2">
                            <button @click="editPlatform(platform)" class="btn-outline btn-sm flex-1">
                                Edit
                            </button>
                            <button @click="viewPlatform(platform)" class="btn-primary btn-sm flex-1">
                                View
                            </button>
                            <button @click="showAnalytics(platform)" class="btn-ghost btn-sm">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>

    <!-- Create Platform Modal -->
    <div x-show="showCreateModal" 
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 z-50 overflow-y-auto"
         style="display: none;">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="showCreateModal = false"></div>
            
            <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <form @submit.prevent="createPlatform()">
                    <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">
                            Create New Platform
                        </h3>
                        
                        <div class="space-y-4">
                            <div>
                                <label class="form-label">Platform Name *</label>
                                <input type="text" x-model="newPlatform.name" required class="form-input" placeholder="My Awesome Platform">
                            </div>
                            
                            <div>
                                <label class="form-label">Description</label>
                                <textarea x-model="newPlatform.description" rows="3" class="form-textarea" placeholder="Describe your platform..."></textarea>
                            </div>
                            
                            <div>
                                <label class="form-label">Tagline</label>
                                <input type="text" x-model="newPlatform.tagline" class="form-input" placeholder="A catchy tagline for your platform">
                            </div>
                            
                            <div class="grid grid-cols-3 gap-4">
                                <div class="flex items-center">
                                    <input type="checkbox" x-model="newPlatform.subscription_enabled" class="form-checkbox">
                                    <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">Subscriptions</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" x-model="newPlatform.ppv_enabled" class="form-checkbox">
                                    <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">Pay-per-view</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" x-model="newPlatform.free_content_enabled" class="form-checkbox">
                                    <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">Free content</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button type="submit" :disabled="loading" class="btn-primary sm:ml-3">
                            <span x-show="!loading">Create Platform</span>
                            <span x-show="loading" class="flex items-center">
                                <div class="spinner w-4 h-4 mr-2"></div>
                                Creating...
                            </span>
                        </button>
                        <button type="button" @click="showCreateModal = false" class="btn-outline mt-3 sm:mt-0">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Platform Modal -->
    <div x-show="showEditModal" 
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 z-50 overflow-y-auto"
         style="display: none;">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="showEditModal = false"></div>
            
            <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
                <form @submit.prevent="updatePlatform()" x-show="editingPlatform">
                    <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">
                            Edit Platform
                        </h3>
                        
                        <div class="space-y-4">
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="form-label">Platform Name *</label>
                                    <input type="text" x-model="editingPlatform.name" required class="form-input">
                                </div>
                                <div>
                                    <label class="form-label">Status</label>
                                    <select x-model="editingPlatform.status" class="form-select">
                                        <option value="draft">Draft</option>
                                        <option value="active">Active</option>
                                        <option value="inactive">Inactive</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div>
                                <label class="form-label">Description</label>
                                <textarea x-model="editingPlatform.description" rows="3" class="form-textarea"></textarea>
                            </div>
                            
                            <div>
                                <label class="form-label">Tagline</label>
                                <input type="text" x-model="editingPlatform.tagline" class="form-input">
                            </div>
                            
                            <div class="grid grid-cols-3 gap-4">
                                <div class="flex items-center">
                                    <input type="checkbox" x-model="editingPlatform.subscription_enabled" class="form-checkbox">
                                    <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">Subscriptions</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" x-model="editingPlatform.ppv_enabled" class="form-checkbox">
                                    <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">Pay-per-view</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" x-model="editingPlatform.free_content_enabled" class="form-checkbox">
                                    <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">Free content</label>
                                </div>
                            </div>
                            
                            <!-- File Uploads -->
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="form-label">Logo</label>
                                    <div class="file-upload" @drop.prevent="handleFileDrop($event, 'logo')" @dragover.prevent @dragenter.prevent>
                                        <input type="file" @change="handleFileUpload($event, 'logo')" accept="image/*" class="hidden" x-ref="logoInput">
                                        <div class="text-center">
                                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                            </svg>
                                            <div class="mt-2">
                                                <button type="button" @click="$refs.logoInput.click()" class="btn-outline btn-sm">Upload Logo</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div>
                                    <label class="form-label">Banner</label>
                                    <div class="file-upload" @drop.prevent="handleFileDrop($event, 'banner')" @dragover.prevent @dragenter.prevent>
                                        <input type="file" @change="handleFileUpload($event, 'banner')" accept="image/*" class="hidden" x-ref="bannerInput">
                                        <div class="text-center">
                                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                            </svg>
                                            <div class="mt-2">
                                                <button type="button" @click="$refs.bannerInput.click()" class="btn-outline btn-sm">Upload Banner</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button type="submit" :disabled="loading" class="btn-primary sm:ml-3">
                            <span x-show="!loading">Update Platform</span>
                            <span x-show="loading" class="flex items-center">
                                <div class="spinner w-4 h-4 mr-2"></div>
                                Updating...
                            </span>
                        </button>
                        <button type="button" @click="showEditModal = false" class="btn-outline mt-3 sm:mt-0">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
