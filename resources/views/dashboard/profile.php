<div x-data="profileApp()" x-init="init()" class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="container py-6">
            <div class="md:flex md:items-center md:justify-between">
                <div class="flex-1 min-w-0">
                    <h2 class="text-2xl font-bold leading-7 text-gray-900 dark:text-gray-100 sm:text-3xl sm:truncate">
                        Profile Settings
                    </h2>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        Manage your account information and preferences
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="container py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Profile Form -->
            <div class="lg:col-span-2">
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">
                            Personal Information
                        </h3>
                    </div>
                    <form @submit.prevent="updateProfile()" class="card-body space-y-6">
                        <!-- Avatar Upload -->
                        <div class="flex items-center space-x-6">
                            <div class="shrink-0">
                                <img class="h-16 w-16 object-cover rounded-full" 
                                     :src="profile.avatar_url || `https://ui-avatars.com/api/?name=${encodeURIComponent(profile.first_name + ' ' + profile.last_name)}`" 
                                     :alt="profile.first_name + ' ' + profile.last_name">
                            </div>
                            <div>
                                <input type="file" @change="uploadAvatar($event)" accept="image/*" class="hidden" x-ref="avatarInput">
                                <button type="button" @click="$refs.avatarInput.click()" class="btn-outline btn-sm">
                                    Change Avatar
                                </button>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                    JPG, GIF or PNG. Max size 5MB.
                                </p>
                            </div>
                        </div>

                        <!-- Name Fields -->
                        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                            <div>
                                <label class="form-label">First Name</label>
                                <input type="text" x-model="profile.first_name" required class="form-input">
                            </div>
                            <div>
                                <label class="form-label">Last Name</label>
                                <input type="text" x-model="profile.last_name" required class="form-input">
                            </div>
                        </div>

                        <!-- Email and Username -->
                        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                            <div>
                                <label class="form-label">Email Address</label>
                                <input type="email" x-model="profile.email" required class="form-input">
                                <p class="form-help">We'll send verification if you change your email.</p>
                            </div>
                            <div>
                                <label class="form-label">Username</label>
                                <input type="text" x-model="profile.username" class="form-input">
                                <p class="form-help">This will be your public profile URL.</p>
                            </div>
                        </div>

                        <!-- Bio -->
                        <div>
                            <label class="form-label">Bio</label>
                            <textarea x-model="profile.bio" rows="4" class="form-textarea" 
                                      placeholder="Tell us about yourself..."></textarea>
                            <p class="form-help">Brief description for your public profile.</p>
                        </div>

                        <!-- Additional Fields -->
                        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                            <div>
                                <label class="form-label">Phone Number</label>
                                <input type="tel" x-model="profile.phone" class="form-input">
                            </div>
                            <div>
                                <label class="form-label">Date of Birth</label>
                                <input type="date" x-model="profile.date_of_birth" class="form-input">
                            </div>
                        </div>

                        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                            <div>
                                <label class="form-label">Country</label>
                                <select x-model="profile.country_code" class="form-select">
                                    <option value="">Select Country</option>
                                    <option value="US">United States</option>
                                    <option value="CA">Canada</option>
                                    <option value="GB">United Kingdom</option>
                                    <option value="AU">Australia</option>
                                    <option value="DE">Germany</option>
                                    <option value="FR">France</option>
                                    <option value="IN">India</option>
                                    <option value="JP">Japan</option>
                                </select>
                            </div>
                            <div>
                                <label class="form-label">Timezone</label>
                                <select x-model="profile.timezone" class="form-select">
                                    <option value="">Select Timezone</option>
                                    <option value="America/New_York">Eastern Time</option>
                                    <option value="America/Chicago">Central Time</option>
                                    <option value="America/Denver">Mountain Time</option>
                                    <option value="America/Los_Angeles">Pacific Time</option>
                                    <option value="Europe/London">London</option>
                                    <option value="Europe/Paris">Paris</option>
                                    <option value="Asia/Tokyo">Tokyo</option>
                                    <option value="Australia/Sydney">Sydney</option>
                                </select>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex justify-end">
                            <button type="submit" :disabled="loading" class="btn-primary">
                                <span x-show="!loading">Save Changes</span>
                                <span x-show="loading" class="flex items-center">
                                    <div class="spinner w-4 h-4 mr-2"></div>
                                    Saving...
                                </span>
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Password Change -->
                <div class="card mt-8">
                    <div class="card-header">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">
                            Change Password
                        </h3>
                    </div>
                    <form @submit.prevent="changePassword()" class="card-body space-y-6">
                        <div>
                            <label class="form-label">Current Password</label>
                            <input type="password" x-model="passwordForm.current_password" required class="form-input">
                        </div>
                        
                        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                            <div>
                                <label class="form-label">New Password</label>
                                <input type="password" x-model="passwordForm.new_password" required class="form-input">
                            </div>
                            <div>
                                <label class="form-label">Confirm New Password</label>
                                <input type="password" x-model="passwordForm.new_password_confirmation" required class="form-input">
                            </div>
                        </div>

                        <div class="flex justify-end">
                            <button type="submit" :disabled="loading" class="btn-primary">
                                <span x-show="!loading">Update Password</span>
                                <span x-show="loading" class="flex items-center">
                                    <div class="spinner w-4 h-4 mr-2"></div>
                                    Updating...
                                </span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Account Status -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">
                            Account Status
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-500 dark:text-gray-400">Email Verified</span>
                                <span :class="profile.email_verified_at ? 'badge-success' : 'badge-warning'" class="badge">
                                    <span x-text="profile.email_verified_at ? 'Verified' : 'Pending'"></span>
                                </span>
                            </div>
                            
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-500 dark:text-gray-400">Account Type</span>
                                <span class="badge-primary badge" x-text="profile.role"></span>
                            </div>
                            
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-500 dark:text-gray-400">Member Since</span>
                                <span class="text-sm text-gray-900 dark:text-gray-100" x-text="FilmStream.utils.formatDate(profile.created_at)"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notification Preferences -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">
                            Notifications
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <label class="text-sm font-medium text-gray-900 dark:text-gray-100">Email Notifications</label>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">Receive updates via email</p>
                                </div>
                                <input type="checkbox" x-model="profile.email_notifications" class="form-checkbox">
                            </div>
                            
                            <div class="flex items-center justify-between">
                                <div>
                                    <label class="text-sm font-medium text-gray-900 dark:text-gray-100">Marketing Emails</label>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">Promotional content and offers</p>
                                </div>
                                <input type="checkbox" x-model="profile.marketing_notifications" class="form-checkbox">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Two-Factor Authentication -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">
                            Two-Factor Authentication
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-500 dark:text-gray-400">Status</span>
                                <span :class="profile.two_factor_enabled ? 'badge-success' : 'badge-gray'" class="badge">
                                    <span x-text="profile.two_factor_enabled ? 'Enabled' : 'Disabled'"></span>
                                </span>
                            </div>
                            
                            <button @click="toggle2FA()" :disabled="loading" class="btn-outline w-full">
                                <span x-text="profile.two_factor_enabled ? 'Disable 2FA' : 'Enable 2FA'"></span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Danger Zone -->
                <div class="card border-red-200 dark:border-red-800">
                    <div class="card-header bg-red-50 dark:bg-red-900/20">
                        <h3 class="text-lg leading-6 font-medium text-red-900 dark:text-red-100">
                            Danger Zone
                        </h3>
                    </div>
                    <div class="card-body">
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                            Once you delete your account, there is no going back. Please be certain.
                        </p>
                        <button @click="showDeleteModal = true" class="btn-danger w-full">
                            Delete Account
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Account Modal -->
    <div x-show="showDeleteModal" 
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 z-50 overflow-y-auto"
         style="display: none;">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="showDeleteModal = false"></div>
            
            <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <form @submit.prevent="deleteAccount()">
                    <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900 sm:mx-0 sm:h-10 sm:w-10">
                                <svg class="h-6 w-6 text-red-600 dark:text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                </svg>
                            </div>
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">
                                    Delete Account
                                </h3>
                                <div class="mt-2">
                                    <p class="text-sm text-gray-500 dark:text-gray-400">
                                        Are you sure you want to delete your account? This action cannot be undone.
                                    </p>
                                </div>
                                
                                <div class="mt-4 space-y-4">
                                    <div>
                                        <label class="form-label">Enter your password to confirm</label>
                                        <input type="password" x-model="deleteForm.password" required class="form-input">
                                    </div>
                                    
                                    <div>
                                        <label class="form-label">Type "DELETE" to confirm</label>
                                        <input type="text" x-model="deleteForm.confirmation" required class="form-input" placeholder="DELETE">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button type="submit" :disabled="loading || deleteForm.confirmation !== 'DELETE'" class="btn-danger sm:ml-3">
                            <span x-show="!loading">Delete Account</span>
                            <span x-show="loading" class="flex items-center">
                                <div class="spinner w-4 h-4 mr-2"></div>
                                Deleting...
                            </span>
                        </button>
                        <button type="button" @click="showDeleteModal = false" class="btn-outline mt-3 sm:mt-0">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
