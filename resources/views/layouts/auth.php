<!DOCTYPE html>
<html lang="en" x-data="{ darkMode: localStorage.getItem('darkMode') === 'true' }" 
      :class="{ 'dark': darkMode }">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'FilmStream - OTT Platform' ?></title>
    <meta name="description" content="<?= $description ?? 'The OTT Platform Built by Filmmakers, for Filmmakers' ?>">
    <meta name="csrf-token" content="<?= $this->csrf() ?>">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?= $this->asset('css/app.css') ?>">
    
    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a'
                        },
                        secondary: {
                            50: '#faf5ff',
                            100: '#f3e8ff',
                            200: '#e9d5ff',
                            300: '#d8b4fe',
                            400: '#c084fc',
                            500: '#a855f7',
                            600: '#9333ea',
                            700: '#7c3aed',
                            800: '#6b21a8',
                            900: '#581c87'
                        }
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    }
                }
            }
        }
    </script>
    
    <!-- Global JavaScript Variables -->
    <script>
        window.FilmStream = {
            apiUrl: '<?= $this->url('api') ?>',
            baseUrl: '<?= $this->url() ?>',
            csrfToken: '<?= $this->csrf() ?>',
            user: null,
            authToken: localStorage.getItem('authToken')
        };
    </script>
</head>
<body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 font-sans antialiased">
    <!-- Notification container -->
    <div id="notifications" class="fixed top-4 right-4 z-40 space-y-2"></div>

    <!-- Main content -->
    <main>
        <?= $content ?>
    </main>

    <!-- Core JavaScript -->
    <script src="<?= $this->asset('js/core.js') ?>"></script>
</body>
</html>
