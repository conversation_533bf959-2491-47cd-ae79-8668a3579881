<nav x-data="navigationApp()" class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
            <div class="flex items-center">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="<?= $this->url() ?>" class="flex items-center">
                        <h1 class="text-2xl font-bold bg-gradient-to-r from-primary-500 to-secondary-500 bg-clip-text text-transparent">
                            FilmStream
                        </h1>
                    </a>
                </div>
                
                <!-- Desktop Navigation -->
                <div class="hidden md:ml-6 md:flex md:space-x-8">
                    <?php if ($this->auth()): ?>
                        <a href="<?= $this->url('dashboard') ?>" 
                           class="border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                            Dashboard
                        </a>
                        
                        <?php if ($this->auth()['role'] === 'filmmaker'): ?>
                            <a href="<?= $this->url('dashboard/platforms') ?>" 
                               class="border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                My Platforms
                            </a>
                            <a href="<?= $this->url('dashboard/content') ?>" 
                               class="border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                Content
                            </a>
                        <?php endif; ?>
                        
                        <a href="<?= $this->url('discover') ?>" 
                           class="border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                            Discover
                        </a>
                    <?php else: ?>
                        <a href="<?= $this->url('discover') ?>" 
                           class="border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                            Discover
                        </a>
                        <a href="<?= $this->url('pricing') ?>" 
                           class="border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                            Pricing
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="flex items-center space-x-4">
                <!-- Search -->
                <div class="hidden md:block">
                    <div class="relative">
                        <input type="text" 
                               placeholder="Search platforms, content..." 
                               class="w-64 pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
                
                <!-- Dark mode toggle -->
                <button @click="toggleDarkMode()" 
                        class="p-2 rounded-md text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-500">
                    <svg x-show="!darkMode" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                    </svg>
                    <svg x-show="darkMode" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </button>
                
                <?php if ($this->auth()): ?>
                    <!-- User menu -->
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open" 
                                class="flex items-center space-x-2 text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <img class="h-8 w-8 rounded-full object-cover" 
                                 src="<?= $this->auth()['avatar_url'] ?? 'https://ui-avatars.com/api/?name=' . urlencode($this->auth()['first_name'] . ' ' . $this->auth()['last_name']) ?>" 
                                 alt="<?= $this->e($this->auth()['first_name'] . ' ' . $this->auth()['last_name']) ?>">
                            <span class="hidden md:block font-medium"><?= $this->e($this->auth()['first_name']) ?></span>
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        
                        <div x-show="open" 
                             @click.away="open = false"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="transform opacity-0 scale-95"
                             x-transition:enter-end="transform opacity-100 scale-100"
                             x-transition:leave="transition ease-in duration-75"
                             x-transition:leave-start="transform opacity-100 scale-100"
                             x-transition:leave-end="transform opacity-0 scale-95"
                             class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-50">
                            <div class="py-1">
                                <a href="<?= $this->url('dashboard/profile') ?>" 
                                   class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    Profile
                                </a>
                                <a href="<?= $this->url('dashboard/settings') ?>" 
                                   class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    Settings
                                </a>
                                <?php if ($this->auth()['role'] === 'admin'): ?>
                                    <a href="<?= $this->url('admin') ?>" 
                                       class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                        Admin Panel
                                    </a>
                                <?php endif; ?>
                                <div class="border-t border-gray-100 dark:border-gray-700"></div>
                                <button @click="logout()" 
                                        class="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    Sign out
                                </button>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- Guest menu -->
                    <div class="flex items-center space-x-4">
                        <a href="<?= $this->url('login') ?>" 
                           class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 px-3 py-2 text-sm font-medium">
                            Sign in
                        </a>
                        <a href="<?= $this->url('register') ?>" 
                           class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            Get Started
                        </a>
                    </div>
                <?php endif; ?>
                
                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button @click="mobileMenuOpen = !mobileMenuOpen" 
                            class="p-2 rounded-md text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-500">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path x-show="!mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            <path x-show="mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Mobile menu -->
    <div x-show="mobileMenuOpen" 
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="opacity-0 scale-95"
         x-transition:enter-end="opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-100"
         x-transition:leave-start="opacity-100 scale-100"
         x-transition:leave-end="opacity-0 scale-95"
         class="md:hidden">
        <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
            <!-- Mobile navigation items -->
            <?php if ($this->auth()): ?>
                <a href="<?= $this->url('dashboard') ?>" 
                   class="text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 block px-3 py-2 rounded-md text-base font-medium">
                    Dashboard
                </a>
                <?php if ($this->auth()['role'] === 'filmmaker'): ?>
                    <a href="<?= $this->url('dashboard/platforms') ?>" 
                       class="text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 block px-3 py-2 rounded-md text-base font-medium">
                        My Platforms
                    </a>
                <?php endif; ?>
            <?php else: ?>
                <a href="<?= $this->url('login') ?>" 
                   class="text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 block px-3 py-2 rounded-md text-base font-medium">
                    Sign in
                </a>
                <a href="<?= $this->url('register') ?>" 
                   class="text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 block px-3 py-2 rounded-md text-base font-medium">
                    Get Started
                </a>
            <?php endif; ?>
        </div>
    </div>
</nav>

<script>
function navigationApp() {
    return {
        mobileMenuOpen: false,
        darkMode: localStorage.getItem('darkMode') === 'true',
        
        toggleDarkMode() {
            this.darkMode = !this.darkMode;
            localStorage.setItem('darkMode', this.darkMode.toString());
        },
        
        async logout() {
            try {
                await FilmStreamAPI.call('/auth/logout', { method: 'POST' });
                localStorage.removeItem('authToken');
                localStorage.removeItem('user');
                window.location.href = '/';
            } catch (error) {
                console.error('Logout failed:', error);
                // Force logout anyway
                localStorage.removeItem('authToken');
                localStorage.removeItem('user');
                window.location.href = '/';
            }
        }
    };
}
</script>
