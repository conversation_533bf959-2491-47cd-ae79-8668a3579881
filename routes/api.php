<?php

declare(strict_types=1);

use App\Core\Router;
use App\Core\Http\Request;
use App\Core\Http\JsonResponse;

/**
 * API Routes
 * RESTful API endpoints
 */

/** @var Router $router */
$router = $app->getRouter();

// API health check
$router->get('/api/health', function (Request $request) {
    return JsonResponse::success([
        'status' => 'healthy',
        'timestamp' => date('c'),
        'version' => '1.0.0'
    ], 'API is running');
})->name('api.health');

// Debug routes
$router->get('/api/debug/routes', function (Request $request) use ($router) {
    $routes = [];
    foreach ($router->getRoutes() as $route) {
        $routes[] = [
            'methods' => $route->getMethods(),
            'uri' => $route->getUri(),
            'regex' => $route->getRegex(),
            'action' => is_string($route->getAction()) ? $route->getAction() : 'Closure'
        ];
    }
    return JsonResponse::success(['routes' => $routes]);
})->name('api.debug.routes');

// Test endpoint without database
$router->post('/api/test/register', function (Request $request) {
    return JsonResponse::success([
        'message' => 'Test registration endpoint working',
        'received_data' => $request->getBody()
    ]);
})->name('api.test.register');

// Authentication API
$router->group(['prefix' => 'api/auth'], function (Router $router) {
    $router->post('/register', 'App\Controllers\AuthController@register')->name('api.auth.register');
    $router->post('/login', 'App\Controllers\AuthController@login')->name('api.auth.login');
    $router->post('/logout', 'App\Controllers\AuthController@logout')->name('api.auth.logout');
    $router->post('/refresh', 'App\Controllers\AuthController@refreshToken')->name('api.auth.refresh');
    $router->post('/forgot-password', 'App\Controllers\AuthController@forgotPassword')->name('api.auth.forgot');
    $router->post('/reset-password', 'App\Controllers\AuthController@resetPassword')->name('api.auth.reset');
    $router->post('/verify-email', 'App\Controllers\AuthController@verifyEmail')->name('api.auth.verify-email');

    // 2FA endpoints
    $router->post('/2fa/verify', 'App\Controllers\AuthController@verify2FA')->name('api.auth.2fa.verify');

    // Protected 2FA management endpoints
    $router->group(['middleware' => ['auth']], function (Router $router) {
        $router->get('/2fa/setup', 'App\Controllers\TwoFactorController@getSetupData')->name('api.auth.2fa.setup');
        $router->post('/2fa/test', 'App\Controllers\TwoFactorController@testSetup')->name('api.auth.2fa.test');
        $router->post('/2fa/enable', 'App\Controllers\TwoFactorController@enable')->name('api.auth.2fa.enable');
        $router->post('/2fa/disable', 'App\Controllers\TwoFactorController@disable')->name('api.auth.2fa.disable');
        $router->post('/2fa/verify-session', 'App\Controllers\TwoFactorController@verify')->name('api.auth.2fa.verify-session');
        $router->get('/2fa/status', 'App\Controllers\TwoFactorController@getStatus')->name('api.auth.2fa.status');
        $router->get('/2fa/recovery-codes', 'App\Controllers\TwoFactorController@getRecoveryCodes')->name('api.auth.2fa.recovery-codes');
        $router->post('/2fa/regenerate-codes', 'App\Controllers\TwoFactorController@regenerateRecoveryCodes')->name('api.auth.2fa.regenerate-codes');
    });
});

// User API
$router->group(['prefix' => 'api/users', 'middleware' => ['auth']], function (Router $router) {
    $router->get('/profile', 'App\Controllers\UserController@getProfile')->name('api.users.profile');
    $router->put('/profile', 'App\Controllers\UserController@updateProfile')->name('api.users.update');
    $router->post('/avatar', 'App\Controllers\UserController@uploadAvatar')->name('api.users.avatar');
    $router->post('/change-password', 'App\Controllers\UserController@changePassword')->name('api.users.change-password');
    $router->delete('/account', 'App\Controllers\UserController@deleteAccount')->name('api.users.delete-account');
    $router->get('/activity', 'App\Controllers\UserController@getActivityLog')->name('api.users.activity');
    $router->get('/subscriptions', 'App\Controllers\UserController@getSubscriptions')->name('api.users.subscriptions');
    $router->get('/watch-history', 'App\Controllers\UserController@getWatchHistory')->name('api.users.watch-history');
});

// Filmmaker API
$router->group(['prefix' => 'api/filmmakers'], function (Router $router) {
    // Public endpoints
    $router->get('/', 'App\Controllers\FilmmakerController@listFilmmakers')->name('api.filmmakers.index');
    $router->get('/{id}', 'App\Controllers\FilmmakerController@getProfile')->name('api.filmmakers.show');

    // Protected endpoints
    $router->group(['middleware' => ['auth', 'role:filmmaker']], function (Router $router) {
        $router->post('/profile', 'App\Controllers\FilmmakerController@createProfile')->name('api.filmmakers.create-profile');
        $router->put('/profile', 'App\Controllers\FilmmakerController@updateProfile')->name('api.filmmakers.update-profile');
        $router->post('/verification', 'App\Controllers\FilmmakerController@submitVerification')->name('api.filmmakers.submit-verification');
        $router->get('/verification/status', 'App\Controllers\FilmmakerController@getVerificationStatus')->name('api.filmmakers.verification-status');
        $router->get('/platforms', 'App\Controllers\FilmmakerController@getPlatforms')->name('api.filmmakers.platforms');
    });
});

// Platform API
$router->group(['prefix' => 'api/platforms'], function (Router $router) {
    // Public endpoints
    $router->get('/', 'App\Controllers\PlatformController@index')->name('api.platforms.index');
    $router->get('/{id}', 'App\Controllers\PlatformController@show')->name('api.platforms.show');

    // Protected endpoints
    $router->group(['middleware' => ['auth', 'role:filmmaker']], function (Router $router) {
        $router->post('/', 'App\Controllers\PlatformController@store')->name('api.platforms.store');
        $router->put('/{id}', 'App\Controllers\PlatformController@update')->name('api.platforms.update');
        $router->delete('/{id}', 'App\Controllers\PlatformController@destroy')->name('api.platforms.destroy');
        $router->post('/{id}/logo', 'App\Controllers\PlatformController@uploadLogo')->name('api.platforms.upload-logo');
        $router->post('/{id}/banner', 'App\Controllers\PlatformController@uploadBanner')->name('api.platforms.upload-banner');
        $router->get('/{id}/analytics', 'App\Controllers\PlatformController@getAnalytics')->name('api.platforms.analytics');
    });
});

// Content API
$router->group(['prefix' => 'api/content'], function (Router $router) {
    $router->get('/', function (Request $request) {
        return JsonResponse::success([], 'List content endpoint - Coming Soon');
    })->name('api.content.index');
    
    $router->post('/', function (Request $request) {
        return JsonResponse::created([], 'Create content endpoint - Coming Soon');
    })->name('api.content.store');
    
    $router->get('/{id}', function (Request $request, string $id) {
        return JsonResponse::success(['id' => $id], 'Get content endpoint - Coming Soon');
    })->name('api.content.show');
    
    $router->put('/{id}', function (Request $request, string $id) {
        return JsonResponse::success(['id' => $id], 'Update content endpoint - Coming Soon');
    })->name('api.content.update');
    
    $router->delete('/{id}', function (Request $request, string $id) {
        return JsonResponse::success(['id' => $id], 'Delete content endpoint - Coming Soon');
    })->name('api.content.destroy');
    
    // Video upload
    $router->post('/{id}/upload', function (Request $request, string $id) {
        return JsonResponse::success(['id' => $id], 'Upload video endpoint - Coming Soon');
    })->name('api.content.upload');
});

// Payment API
$router->group(['prefix' => 'api/payments'], function (Router $router) {
    $router->post('/stripe/connect', function (Request $request) {
        return JsonResponse::success([], 'Stripe Connect endpoint - Coming Soon');
    })->name('api.payments.stripe.connect');
    
    $router->post('/subscribe', function (Request $request) {
        return JsonResponse::success([], 'Subscribe endpoint - Coming Soon');
    })->name('api.payments.subscribe');
    
    $router->post('/purchase', function (Request $request) {
        return JsonResponse::success([], 'Purchase endpoint - Coming Soon');
    })->name('api.payments.purchase');
    
    $router->post('/webhooks/stripe', function (Request $request) {
        return JsonResponse::success([], 'Stripe webhook endpoint - Coming Soon');
    })->name('api.payments.webhooks.stripe');
});

// Analytics API
$router->group(['prefix' => 'api/analytics'], function (Router $router) {
    $router->get('/dashboard', function (Request $request) {
        return JsonResponse::success([], 'Dashboard analytics endpoint - Coming Soon');
    })->name('api.analytics.dashboard');
    
    $router->get('/content/{id}', function (Request $request, string $id) {
        return JsonResponse::success(['id' => $id], 'Content analytics endpoint - Coming Soon');
    })->name('api.analytics.content');
    
    $router->get('/revenue', function (Request $request) {
        return JsonResponse::success([], 'Revenue analytics endpoint - Coming Soon');
    })->name('api.analytics.revenue');
});

// Search API
$router->group(['prefix' => 'api/search'], function (Router $router) {
    $router->get('/content', function (Request $request) {
        $query = $request->query('q', '');
        return JsonResponse::success(['query' => $query], 'Search content endpoint - Coming Soon');
    })->name('api.search.content');
    
    $router->get('/filmmakers', function (Request $request) {
        $query = $request->query('q', '');
        return JsonResponse::success(['query' => $query], 'Search filmmakers endpoint - Coming Soon');
    })->name('api.search.filmmakers');
});

// Admin API
$router->group(['prefix' => 'api/admin', 'middleware' => ['auth', 'role:admin']], function (Router $router) {
    $router->get('/dashboard', 'App\Controllers\AdminController@getDashboardStats')->name('api.admin.dashboard');
    $router->get('/users', 'App\Controllers\AdminController@listUsers')->name('api.admin.users');
    $router->put('/users/{id}/status', 'App\Controllers\AdminController@updateUserStatus')->name('api.admin.users.status');
    $router->get('/verification-queue', 'App\Controllers\AdminController@getVerificationQueue')->name('api.admin.verification-queue');
    $router->post('/verification/{userId}/approve', 'App\Controllers\AdminController@approveVerification')->name('api.admin.verification.approve');
    $router->post('/verification/{userId}/reject', 'App\Controllers\AdminController@rejectVerification')->name('api.admin.verification.reject');
    $router->get('/activity-logs', 'App\Controllers\AdminController@getActivityLogs')->name('api.admin.activity-logs');
});

// Video streaming API
$router->group(['prefix' => 'api/stream'], function (Router $router) {
    $router->get('/{id}/url', function (Request $request, string $id) {
        return JsonResponse::success(['id' => $id], 'Get stream URL endpoint - Coming Soon');
    })->name('api.stream.url');
    
    $router->post('/{id}/view', function (Request $request, string $id) {
        return JsonResponse::success(['id' => $id], 'Track view endpoint - Coming Soon');
    })->name('api.stream.view');
});
