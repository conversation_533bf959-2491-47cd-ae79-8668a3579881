<?php

declare(strict_types=1);

use App\Core\Router;
use App\Core\Http\Request;
use App\Core\Http\Response;

/**
 * Web Routes
 * Routes for the web interface
 */

/** @var Router $router */
$router = $app->getRouter();

// Home page
$router->get('/', function (Request $request) {
    return new Response('
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>FilmStream - OTT Platform</title>
            <script src="https://cdn.tailwindcss.com"></script>
            <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
        </head>
        <body class="bg-gray-900 text-white">
            <div class="min-h-screen flex items-center justify-center">
                <div class="text-center">
                    <h1 class="text-6xl font-bold mb-4 bg-gradient-to-r from-blue-500 to-purple-500 bg-clip-text text-transparent">
                        FilmStream
                    </h1>
                    <p class="text-xl mb-8 text-gray-300">
                        The OTT Platform Built by Filmmakers, for Filmmakers
                    </p>
                    <div class="space-x-4">
                        <a href="/register" class="bg-red-600 hover:bg-red-700 px-6 py-3 rounded-lg font-semibold transition">
                            Start Your Platform
                        </a>
                        <a href="/login" class="border border-gray-600 hover:border-gray-500 px-6 py-3 rounded-lg font-semibold transition">
                            Sign In
                        </a>
                    </div>
                </div>
            </div>
        </body>
        </html>
    ');
})->name('home');

// Authentication routes
$router->get('/login', function (Request $request) {
    return new Response('Login Page - Coming Soon');
})->name('login');

$router->get('/register', function (Request $request) {
    return new Response('Register Page - Coming Soon');
})->name('register');

// Dashboard routes (protected)
$router->group(['prefix' => 'dashboard'], function (Router $router) {
    $router->get('/', function (Request $request) {
        return new Response('Dashboard - Coming Soon');
    })->name('dashboard');
    
    $router->get('/content', function (Request $request) {
        return new Response('Content Management - Coming Soon');
    })->name('dashboard.content');
    
    $router->get('/analytics', function (Request $request) {
        return new Response('Analytics - Coming Soon');
    })->name('dashboard.analytics');
});

// Filmmaker platform routes
$router->group(['prefix' => 'platform'], function (Router $router) {
    $router->get('/create', function (Request $request) {
        return new Response('Create Platform - Coming Soon');
    })->name('platform.create');
    
    $router->get('/{slug}', function (Request $request, string $slug) {
        return new Response("Filmmaker Platform: {$slug} - Coming Soon");
    })->name('platform.show');
});

// Content discovery
$router->get('/discover', function (Request $request) {
    return new Response('Content Discovery - Coming Soon');
})->name('discover');

$router->get('/browse', function (Request $request) {
    return new Response('Browse Content - Coming Soon');
})->name('browse');

// Video player
$router->get('/watch/{id}', function (Request $request, string $id) {
    return new Response("Video Player for ID: {$id} - Coming Soon");
})->name('watch');

// Static pages
$router->get('/about', function (Request $request) {
    return new Response('About Us - Coming Soon');
})->name('about');

$router->get('/pricing', function (Request $request) {
    return new Response('Pricing - Coming Soon');
})->name('pricing');

$router->get('/contact', function (Request $request) {
    return new Response('Contact Us - Coming Soon');
})->name('contact');
