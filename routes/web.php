<?php

declare(strict_types=1);

use App\Core\Router;
use App\Core\Http\Request;
use App\Core\Http\Response;

/**
 * Web Routes
 * Routes for the web interface
 */

/** @var Router $router */
$router = $app->getRouter();

// Home page
$router->get('/', function (Request $request) {
    ob_start();
    include ROOT_PATH . '/resources/views/home.php';
    return new Response(ob_get_clean());
})->name('home');

// Authentication pages
$router->get('/login', function (Request $request) {
    ob_start();
    include ROOT_PATH . '/resources/views/auth/login.php';
    return new Response(ob_get_clean());
})->name('login');

$router->get('/register', function (Request $request) {
    ob_start();
    include ROOT_PATH . '/resources/views/auth/register.php';
    return new Response(ob_get_clean());
})->name('register');

// Dashboard (protected)
$router->get('/dashboard', function (Request $request) {
    ob_start();
    include ROOT_PATH . '/resources/views/dashboard/dashboard.php';
    return new Response(ob_get_clean());
})->name('dashboard');

// Platform management
$router->get('/dashboard/platforms', function (Request $request) {
    ob_start();
    include ROOT_PATH . '/resources/views/dashboard/platforms.php';
    return new Response(ob_get_clean());
})->name('dashboard.platforms');

// Duplicate routes removed - using the HTML file versions above

// Dashboard routes (protected)
$router->group(['prefix' => 'dashboard'], function (Router $router) {
    $router->get('/', function (Request $request) {
        return new Response('Dashboard - Coming Soon');
    })->name('dashboard');
    
    $router->get('/content', function (Request $request) {
        return new Response('Content Management - Coming Soon');
    })->name('dashboard.content');
    
    $router->get('/analytics', function (Request $request) {
        return new Response('Analytics - Coming Soon');
    })->name('dashboard.analytics');
});

// Filmmaker platform routes
$router->group(['prefix' => 'platform'], function (Router $router) {
    $router->get('/create', function (Request $request) {
        return new Response('Create Platform - Coming Soon');
    })->name('platform.create');
    
    $router->get('/{slug}', function (Request $request, string $slug) {
        return new Response("Filmmaker Platform: {$slug} - Coming Soon");
    })->name('platform.show');
});

// Content discovery
$router->get('/discover', function (Request $request) {
    return new Response('Content Discovery - Coming Soon');
})->name('discover');

$router->get('/browse', function (Request $request) {
    return new Response('Browse Content - Coming Soon');
})->name('browse');

// Video player
$router->get('/watch/{id}', function (Request $request, string $id) {
    return new Response("Video Player for ID: {$id} - Coming Soon");
})->name('watch');

// Static pages
$router->get('/about', function (Request $request) {
    return new Response('About Us - Coming Soon');
})->name('about');

$router->get('/pricing', function (Request $request) {
    return new Response('Pricing - Coming Soon');
})->name('pricing');

$router->get('/contact', function (Request $request) {
    return new Response('Contact Us - Coming Soon');
})->name('contact');
