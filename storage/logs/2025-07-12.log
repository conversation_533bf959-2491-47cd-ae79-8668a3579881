[2025-07-12 13:17:51] error: Application error: App\Core\Route::matches(): Return value must be of type bool, int returned {"exception":{},"file":"\/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Route.php","line":85,"trace":"#0 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(159): App\\Core\\Route->matches('GET', '\/')\n#1 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(67): App\\Core\\Router->dispatch(Object(App\\Core\\Http\\Request))\n#2 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Middleware\/RateLimitMiddleware.php(44): App\\Core\\Application->App\\Core\\{closure}()\n#3 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(79): App\\Middleware\\RateLimitMiddleware->handle(Object(App\\Core\\Http\\Request), Object(Closure))\n#4 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Middleware\/SecurityHeadersMiddleware.php(18): App\\Core\\Application->App\\Core\\{closure}()\n#5 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(79): App\\Middleware\\SecurityHeadersMiddleware->handle(Object(App\\Core\\Http\\Request), Object(Closure))\n#6 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Middleware\/CorsMiddleware.php(32): App\\Core\\Application->App\\Core\\{closure}()\n#7 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(79): App\\Middleware\\CorsMiddleware->handle(Object(App\\Core\\Http\\Request), Object(Closure))\n#8 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(51): App\\Core\\Application->App\\Core\\{closure}()\n#9 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/public\/index.php(41): App\\Core\\Application->handle()\n#10 {main}"}
[2025-07-12 13:32:40] error: Application error: Cannot resolve parameter container without type hint {"exception":{},"file":"\/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Container.php","line":149,"trace":"#0 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Container.php(129): App\\Core\\Container->resolveDependency(Object(ReflectionParameter))\n#1 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Container.php(116): App\\Core\\Container->resolveDependencies(Array)\n#2 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Container.php(87): App\\Core\\Container->buildClass('App\\\\Controllers...')\n#3 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Container.php(65): App\\Core\\Container->build('App\\\\Controllers...')\n#4 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(233): App\\Core\\Container->get('App\\\\Controllers...')\n#5 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(178): App\\Core\\Router->callAction(Object(App\\Core\\Http\\Request), Object(App\\Core\\Route), Array)\n#6 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(196): App\\Core\\Router->App\\Core\\{closure}()\n#7 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(179): App\\Core\\Router->runRouteMiddleware(Object(App\\Core\\Http\\Request), Object(App\\Core\\Route), Object(Closure))\n#8 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(159): App\\Core\\Router->runRoute(Object(App\\Core\\Http\\Request), Object(App\\Core\\Route))\n#9 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(67): App\\Core\\Router->dispatch(Object(App\\Core\\Http\\Request))\n#10 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Middleware\/RateLimitMiddleware.php(44): App\\Core\\Application->App\\Core\\{closure}()\n#11 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(79): App\\Middleware\\RateLimitMiddleware->handle(Object(App\\Core\\Http\\Request), Object(Closure))\n#12 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Middleware\/SecurityHeadersMiddleware.php(18): App\\Core\\Application->App\\Core\\{closure}()\n#13 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(79): App\\Middleware\\SecurityHeadersMiddleware->handle(Object(App\\Core\\Http\\Request), Object(Closure))\n#14 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Middleware\/CorsMiddleware.php(32): App\\Core\\Application->App\\Core\\{closure}()\n#15 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(79): App\\Middleware\\CorsMiddleware->handle(Object(App\\Core\\Http\\Request), Object(Closure))\n#16 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(51): App\\Core\\Application->App\\Core\\{closure}()\n#17 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/public\/index.php(41): App\\Core\\Application->handle()\n#18 {main}"}
[2025-07-12 13:33:19] error: Application error: Cannot resolve built-in type string for parameter method {"exception":{},"file":"\/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Container.php","line":161,"trace":"#0 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Container.php(129): App\\Core\\Container->resolveDependency(Object(ReflectionParameter))\n#1 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Container.php(116): App\\Core\\Container->resolveDependencies(Array)\n#2 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Container.php(87): App\\Core\\Container->buildClass('App\\\\Core\\\\Http\\\\R...')\n#3 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Container.php(65): App\\Core\\Container->build('App\\\\Core\\\\Http\\\\R...')\n#4 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Controller.php(28): App\\Core\\Container->get('App\\\\Core\\\\Http\\\\R...')\n#5 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Controllers\/AuthController.php(27): App\\Core\\Controller->__construct(Object(App\\Core\\Container))\n#6 [internal function]: App\\Controllers\\AuthController->__construct(Object(App\\Core\\Container))\n#7 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Container.php(118): ReflectionClass->newInstanceArgs(Array)\n#8 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Container.php(87): App\\Core\\Container->buildClass('App\\\\Controllers...')\n#9 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Container.php(65): App\\Core\\Container->build('App\\\\Controllers...')\n#10 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(233): App\\Core\\Container->get('App\\\\Controllers...')\n#11 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(178): App\\Core\\Router->callAction(Object(App\\Core\\Http\\Request), Object(App\\Core\\Route), Array)\n#12 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(196): App\\Core\\Router->App\\Core\\{closure}()\n#13 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(179): App\\Core\\Router->runRouteMiddleware(Object(App\\Core\\Http\\Request), Object(App\\Core\\Route), Object(Closure))\n#14 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(159): App\\Core\\Router->runRoute(Object(App\\Core\\Http\\Request), Object(App\\Core\\Route))\n#15 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(67): App\\Core\\Router->dispatch(Object(App\\Core\\Http\\Request))\n#16 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Middleware\/RateLimitMiddleware.php(44): App\\Core\\Application->App\\Core\\{closure}()\n#17 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(79): App\\Middleware\\RateLimitMiddleware->handle(Object(App\\Core\\Http\\Request), Object(Closure))\n#18 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Middleware\/SecurityHeadersMiddleware.php(18): App\\Core\\Application->App\\Core\\{closure}()\n#19 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(79): App\\Middleware\\SecurityHeadersMiddleware->handle(Object(App\\Core\\Http\\Request), Object(Closure))\n#20 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Middleware\/CorsMiddleware.php(32): App\\Core\\Application->App\\Core\\{closure}()\n#21 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(79): App\\Middleware\\CorsMiddleware->handle(Object(App\\Core\\Http\\Request), Object(Closure))\n#22 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(51): App\\Core\\Application->App\\Core\\{closure}()\n#23 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/public\/index.php(41): App\\Core\\Application->handle()\n#24 {main}"}
[2025-07-12 13:36:10] error: Registration failed: Cannot resolve built-in type string for parameter method
[2025-07-12 13:41:27] error: Registration failed: Cannot resolve built-in type string for parameter method
[2025-07-12 14:05:42] error: Application error: App\Core\View::layout(): Argument #1 ($layout) must be of type string, null given, called in /Users/<USER>/Downloads/PHP Scripts/Filmmaker_Netflix/routes/web.php on line 56 {"exception":{},"file":"\/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/View.php","line":35,"trace":"#0 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/routes\/web.php(56): App\\Core\\View->layout(NULL)\n#1 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(218): {closure}(Object(App\\Core\\Http\\Request))\n#2 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(178): App\\Core\\Router->callAction(Object(App\\Core\\Http\\Request), Object(App\\Core\\Route), Array)\n#3 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(196): App\\Core\\Router->App\\Core\\{closure}()\n#4 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(179): App\\Core\\Router->runRouteMiddleware(Object(App\\Core\\Http\\Request), Object(App\\Core\\Route), Object(Closure))\n#5 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(159): App\\Core\\Router->runRoute(Object(App\\Core\\Http\\Request), Object(App\\Core\\Route))\n#6 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(67): App\\Core\\Router->dispatch(Object(App\\Core\\Http\\Request))\n#7 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Middleware\/RateLimitMiddleware.php(44): App\\Core\\Application->App\\Core\\{closure}()\n#8 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(79): App\\Middleware\\RateLimitMiddleware->handle(Object(App\\Core\\Http\\Request), Object(Closure))\n#9 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Middleware\/SecurityHeadersMiddleware.php(18): App\\Core\\Application->App\\Core\\{closure}()\n#10 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(79): App\\Middleware\\SecurityHeadersMiddleware->handle(Object(App\\Core\\Http\\Request), Object(Closure))\n#11 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Middleware\/CorsMiddleware.php(32): App\\Core\\Application->App\\Core\\{closure}()\n#12 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(79): App\\Middleware\\CorsMiddleware->handle(Object(App\\Core\\Http\\Request), Object(Closure))\n#13 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(51): App\\Core\\Application->App\\Core\\{closure}()\n#14 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/public\/index.php(41): App\\Core\\Application->handle()\n#15 {main}"}
[2025-07-12 14:05:54] error: Application error: Cannot assign null to property App\Core\View::$layout of type string {"exception":{},"file":"\/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/View.php","line":37,"trace":"#0 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/routes\/web.php(56): App\\Core\\View->layout(NULL)\n#1 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(218): {closure}(Object(App\\Core\\Http\\Request))\n#2 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(178): App\\Core\\Router->callAction(Object(App\\Core\\Http\\Request), Object(App\\Core\\Route), Array)\n#3 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(196): App\\Core\\Router->App\\Core\\{closure}()\n#4 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(179): App\\Core\\Router->runRouteMiddleware(Object(App\\Core\\Http\\Request), Object(App\\Core\\Route), Object(Closure))\n#5 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(159): App\\Core\\Router->runRoute(Object(App\\Core\\Http\\Request), Object(App\\Core\\Route))\n#6 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(67): App\\Core\\Router->dispatch(Object(App\\Core\\Http\\Request))\n#7 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Middleware\/RateLimitMiddleware.php(44): App\\Core\\Application->App\\Core\\{closure}()\n#8 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(79): App\\Middleware\\RateLimitMiddleware->handle(Object(App\\Core\\Http\\Request), Object(Closure))\n#9 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Middleware\/SecurityHeadersMiddleware.php(18): App\\Core\\Application->App\\Core\\{closure}()\n#10 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(79): App\\Middleware\\SecurityHeadersMiddleware->handle(Object(App\\Core\\Http\\Request), Object(Closure))\n#11 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Middleware\/CorsMiddleware.php(32): App\\Core\\Application->App\\Core\\{closure}()\n#12 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(79): App\\Middleware\\CorsMiddleware->handle(Object(App\\Core\\Http\\Request), Object(Closure))\n#13 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(51): App\\Core\\Application->App\\Core\\{closure}()\n#14 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/public\/index.php(41): App\\Core\\Application->handle()\n#15 {main}"}
[2025-07-12 14:06:03] error: Application error: Cannot assign null to property App\Core\View::$layout of type string {"exception":{},"file":"\/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/View.php","line":37,"trace":"#0 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/routes\/web.php(56): App\\Core\\View->layout(NULL)\n#1 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(218): {closure}(Object(App\\Core\\Http\\Request))\n#2 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(178): App\\Core\\Router->callAction(Object(App\\Core\\Http\\Request), Object(App\\Core\\Route), Array)\n#3 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(196): App\\Core\\Router->App\\Core\\{closure}()\n#4 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(179): App\\Core\\Router->runRouteMiddleware(Object(App\\Core\\Http\\Request), Object(App\\Core\\Route), Object(Closure))\n#5 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(159): App\\Core\\Router->runRoute(Object(App\\Core\\Http\\Request), Object(App\\Core\\Route))\n#6 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(67): App\\Core\\Router->dispatch(Object(App\\Core\\Http\\Request))\n#7 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Middleware\/RateLimitMiddleware.php(44): App\\Core\\Application->App\\Core\\{closure}()\n#8 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(79): App\\Middleware\\RateLimitMiddleware->handle(Object(App\\Core\\Http\\Request), Object(Closure))\n#9 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Middleware\/SecurityHeadersMiddleware.php(18): App\\Core\\Application->App\\Core\\{closure}()\n#10 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(79): App\\Middleware\\SecurityHeadersMiddleware->handle(Object(App\\Core\\Http\\Request), Object(Closure))\n#11 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Middleware\/CorsMiddleware.php(32): App\\Core\\Application->App\\Core\\{closure}()\n#12 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(79): App\\Middleware\\CorsMiddleware->handle(Object(App\\Core\\Http\\Request), Object(Closure))\n#13 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(51): App\\Core\\Application->App\\Core\\{closure}()\n#14 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/public\/index.php(41): App\\Core\\Application->handle()\n#15 {main}"}
[2025-07-12 14:06:04] error: Application error: Cannot assign null to property App\Core\View::$layout of type string {"exception":{},"file":"\/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/View.php","line":37,"trace":"#0 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/routes\/web.php(56): App\\Core\\View->layout(NULL)\n#1 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(218): {closure}(Object(App\\Core\\Http\\Request))\n#2 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(178): App\\Core\\Router->callAction(Object(App\\Core\\Http\\Request), Object(App\\Core\\Route), Array)\n#3 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(196): App\\Core\\Router->App\\Core\\{closure}()\n#4 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(179): App\\Core\\Router->runRouteMiddleware(Object(App\\Core\\Http\\Request), Object(App\\Core\\Route), Object(Closure))\n#5 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(159): App\\Core\\Router->runRoute(Object(App\\Core\\Http\\Request), Object(App\\Core\\Route))\n#6 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(67): App\\Core\\Router->dispatch(Object(App\\Core\\Http\\Request))\n#7 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Middleware\/RateLimitMiddleware.php(44): App\\Core\\Application->App\\Core\\{closure}()\n#8 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(79): App\\Middleware\\RateLimitMiddleware->handle(Object(App\\Core\\Http\\Request), Object(Closure))\n#9 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Middleware\/SecurityHeadersMiddleware.php(18): App\\Core\\Application->App\\Core\\{closure}()\n#10 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(79): App\\Middleware\\SecurityHeadersMiddleware->handle(Object(App\\Core\\Http\\Request), Object(Closure))\n#11 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Middleware\/CorsMiddleware.php(32): App\\Core\\Application->App\\Core\\{closure}()\n#12 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(79): App\\Middleware\\CorsMiddleware->handle(Object(App\\Core\\Http\\Request), Object(Closure))\n#13 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(51): App\\Core\\Application->App\\Core\\{closure}()\n#14 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/public\/index.php(41): App\\Core\\Application->handle()\n#15 {main}"}
[2025-07-12 14:06:07] error: Application error: Cannot assign null to property App\Core\View::$layout of type string {"exception":{},"file":"\/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/View.php","line":37,"trace":"#0 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/routes\/web.php(56): App\\Core\\View->layout(NULL)\n#1 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(218): {closure}(Object(App\\Core\\Http\\Request))\n#2 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(178): App\\Core\\Router->callAction(Object(App\\Core\\Http\\Request), Object(App\\Core\\Route), Array)\n#3 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(196): App\\Core\\Router->App\\Core\\{closure}()\n#4 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(179): App\\Core\\Router->runRouteMiddleware(Object(App\\Core\\Http\\Request), Object(App\\Core\\Route), Object(Closure))\n#5 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(159): App\\Core\\Router->runRoute(Object(App\\Core\\Http\\Request), Object(App\\Core\\Route))\n#6 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(67): App\\Core\\Router->dispatch(Object(App\\Core\\Http\\Request))\n#7 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Middleware\/RateLimitMiddleware.php(44): App\\Core\\Application->App\\Core\\{closure}()\n#8 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(79): App\\Middleware\\RateLimitMiddleware->handle(Object(App\\Core\\Http\\Request), Object(Closure))\n#9 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Middleware\/SecurityHeadersMiddleware.php(18): App\\Core\\Application->App\\Core\\{closure}()\n#10 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(79): App\\Middleware\\SecurityHeadersMiddleware->handle(Object(App\\Core\\Http\\Request), Object(Closure))\n#11 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Middleware\/CorsMiddleware.php(32): App\\Core\\Application->App\\Core\\{closure}()\n#12 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(79): App\\Middleware\\CorsMiddleware->handle(Object(App\\Core\\Http\\Request), Object(Closure))\n#13 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(51): App\\Core\\Application->App\\Core\\{closure}()\n#14 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/public\/index.php(41): App\\Core\\Application->handle()\n#15 {main}"}
[2025-07-12 14:06:09] error: Application error: Cannot assign null to property App\Core\View::$layout of type string {"exception":{},"file":"\/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/View.php","line":37,"trace":"#0 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/routes\/web.php(56): App\\Core\\View->layout(NULL)\n#1 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(218): {closure}(Object(App\\Core\\Http\\Request))\n#2 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(178): App\\Core\\Router->callAction(Object(App\\Core\\Http\\Request), Object(App\\Core\\Route), Array)\n#3 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(196): App\\Core\\Router->App\\Core\\{closure}()\n#4 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(179): App\\Core\\Router->runRouteMiddleware(Object(App\\Core\\Http\\Request), Object(App\\Core\\Route), Object(Closure))\n#5 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(159): App\\Core\\Router->runRoute(Object(App\\Core\\Http\\Request), Object(App\\Core\\Route))\n#6 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(67): App\\Core\\Router->dispatch(Object(App\\Core\\Http\\Request))\n#7 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Middleware\/RateLimitMiddleware.php(44): App\\Core\\Application->App\\Core\\{closure}()\n#8 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(79): App\\Middleware\\RateLimitMiddleware->handle(Object(App\\Core\\Http\\Request), Object(Closure))\n#9 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Middleware\/SecurityHeadersMiddleware.php(18): App\\Core\\Application->App\\Core\\{closure}()\n#10 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(79): App\\Middleware\\SecurityHeadersMiddleware->handle(Object(App\\Core\\Http\\Request), Object(Closure))\n#11 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Middleware\/CorsMiddleware.php(32): App\\Core\\Application->App\\Core\\{closure}()\n#12 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(79): App\\Middleware\\CorsMiddleware->handle(Object(App\\Core\\Http\\Request), Object(Closure))\n#13 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(51): App\\Core\\Application->App\\Core\\{closure}()\n#14 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/public\/index.php(41): App\\Core\\Application->handle()\n#15 {main}"}
[2025-07-12 14:06:11] error: Application error: Cannot assign null to property App\Core\View::$layout of type string {"exception":{},"file":"\/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/View.php","line":37,"trace":"#0 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/routes\/web.php(63): App\\Core\\View->layout(NULL)\n#1 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(218): {closure}(Object(App\\Core\\Http\\Request))\n#2 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(178): App\\Core\\Router->callAction(Object(App\\Core\\Http\\Request), Object(App\\Core\\Route), Array)\n#3 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(196): App\\Core\\Router->App\\Core\\{closure}()\n#4 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(179): App\\Core\\Router->runRouteMiddleware(Object(App\\Core\\Http\\Request), Object(App\\Core\\Route), Object(Closure))\n#5 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Router.php(159): App\\Core\\Router->runRoute(Object(App\\Core\\Http\\Request), Object(App\\Core\\Route))\n#6 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(67): App\\Core\\Router->dispatch(Object(App\\Core\\Http\\Request))\n#7 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Middleware\/RateLimitMiddleware.php(44): App\\Core\\Application->App\\Core\\{closure}()\n#8 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(79): App\\Middleware\\RateLimitMiddleware->handle(Object(App\\Core\\Http\\Request), Object(Closure))\n#9 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Middleware\/SecurityHeadersMiddleware.php(18): App\\Core\\Application->App\\Core\\{closure}()\n#10 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(79): App\\Middleware\\SecurityHeadersMiddleware->handle(Object(App\\Core\\Http\\Request), Object(Closure))\n#11 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Middleware\/CorsMiddleware.php(32): App\\Core\\Application->App\\Core\\{closure}()\n#12 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(79): App\\Middleware\\CorsMiddleware->handle(Object(App\\Core\\Http\\Request), Object(Closure))\n#13 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/app\/Core\/Application.php(51): App\\Core\\Application->App\\Core\\{closure}()\n#14 \/Users\/<USER>\/Downloads\/PHP Scripts\/Filmmaker_Netflix\/public\/index.php(41): App\\Core\\Application->handle()\n#15 {main}"}
